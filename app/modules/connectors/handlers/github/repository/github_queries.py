"""
GitHub Connector Neo4j Queries

This module contains all Neo4j queries for the GitHub connector,
organized by entity type and functionality following Google Drive patterns.
"""

from app.modules.connectors.handlers.github.models.schema_loader import github_schema
from app.modules.organisation.models.schema_loader import schema as organisation_schema
from app.modules.agents.models.schema_loader import agent_schema
import structlog

logger = structlog.get_logger()


class GitHubUserQueries:
    """Queries for managing GitHub users following Google Drive patterns."""

    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.org_label = organisation_schema.get_node_labels()[0]  # Organisation
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.contributes_to_rel = github_schema.get_relationship_types()[1]  # CONTRIBUTES_TO
        self.owns_repository_rel = github_schema.get_relationship_types()[0]  # OWNS_REPOSITORY

    @property
    def CREATE_OR_FIND_USER_BY_LOGIN(self):
        """Create or find a GitHub user by login, following Google Drive pattern."""
        return f"""
        // First, try to find existing user by login
        MERGE (gh_user:{self.github_user_label} {{login: $login}})
        ON CREATE SET
            gh_user.id = $id,
            gh_user.organisation_id = $organisation_id,
            gh_user.name = $name,
            gh_user.email = $email,
            gh_user.html_url = $html_url,
            gh_user.type = $type,
            gh_user.created_at = $created_at,
            gh_user.updated_at = $updated_at
        ON MATCH SET
            gh_user.name = $name,
            gh_user.email = $email,
            gh_user.html_url = $html_url,
            gh_user.updated_at = $updated_at
        WITH gh_user
        // Ensure user is connected to the organisation
        MATCH (org:{self.org_label} {{id: $organisation_id}})
        WITH gh_user, org
        RETURN gh_user
        """

    @property
    def CREATE_USER_REPOSITORY_ACCESS(self):
        """Create user-repository access relationship."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{login: $login}})
        MATCH (repo:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (gh_user)-[r:{self.contributes_to_rel}]->(repo)
        SET r.role = $role,
            r.first_contribution_at = $granted_at,
            r.last_contribution_at = $granted_at
        RETURN r
        """

    @property
    def CREATE_USER_REPOSITORY_OWNERSHIP(self):
        """Create user-repository ownership relationship."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{login: $login}})
        MATCH (repo:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (gh_user)-[r:{self.owns_repository_rel}]->(repo)
        SET r.ownership_type = $ownership_type,
            r.granted_at = $granted_at
        RETURN r
        """

class GitHubOrganizationQueries:
    """Queries for managing GitHub organizations following Google Drive patterns."""

    def __init__(self):
        self.org_label = organisation_schema.get_node_labels()[0]  # Organisation
        self.github_org_label = github_schema.get_node_labels()[2]  # GitHubOrganization
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.member_of_rel = github_schema.get_relationship_types()[32]  # MEMBER_OF_ORG
        self.org_owns_repo_rel = github_schema.get_relationship_types()[5]  # ORG_OWNS_REPO

    @property
    def CREATE_OR_UPDATE_GITHUB_ORGANIZATION(self):
        """Create or update a GitHub organization."""
        return f"""
        MERGE (gh_org:{self.github_org_label} {{id: $id}})
        ON CREATE SET
            gh_org.login = $login,
            gh_org.name = $name,
            gh_org.description = $description,
            gh_org.html_url = $html_url,
            gh_org.organisation_id = $organisation_id,
            gh_org.public_repos = $public_repos,
            gh_org.public_gists = $public_gists,
            gh_org.followers = $followers,
            gh_org.following = $following,
            gh_org.created_at = $created_at,
            gh_org.updated_at = $updated_at
        ON MATCH SET
            gh_org.login = $login,
            gh_org.name = $name,
            gh_org.description = $description,
            gh_org.html_url = $html_url,
            gh_org.public_repos = $public_repos,
            gh_org.public_gists = $public_gists,
            gh_org.followers = $followers,
            gh_org.following = $following,
            gh_org.updated_at = $updated_at
        WITH gh_org
        // Link to organisation
        MATCH (org:{self.org_label} {{id: $organisation_id}})
        RETURN gh_org
        """

    @property
    def ADD_MEMBER_TO_ORGANIZATION(self):
        """Add a user as a member of a GitHub organization."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{login: $login}})
        MATCH (gh_org:{self.github_org_label} {{id: $github_org_id}})
        MERGE (gh_user)-[r:{self.member_of_rel}]->(gh_org)
        SET r.role = $role,
            r.join_date = $join_date
        RETURN r
        """

    @property
    def ADD_REPOSITORY_TO_ORGANIZATION(self):
        """Add a repository to a GitHub organization."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MATCH (gh_org:{self.github_org_label} {{id: $github_org_id}})
        MERGE (repo)-[r:{self.org_owns_repo_rel}]->(gh_org)
        SET r.created_at = $created_at
        RETURN r
        """

    @property
    def GET_ORGANIZATION_REPOSITORIES(self):
        """Get all repositories for a GitHub organization."""
        return f"""
        MATCH (repo:{self.repository_label})-[:{self.org_owns_repo_rel}]->(gh_org:{self.github_org_label} {{id: $github_org_id}})
        RETURN repo
        ORDER BY repo.name
        """

    @property
    def GET_ORGANIZATION_MEMBERS(self):
        """Get all members of a GitHub organization."""
        return f"""
        MATCH (gh_user:{self.github_user_label})-[r:{self.member_of_rel}]->(gh_org:{self.github_org_label} {{id: $github_org_id}})
        RETURN gh_user, r.role as role
        ORDER BY gh_user.login
        """

    @property
    def CREATE_COMPREHENSIVE_ORGANIZATION_ECOSYSTEM(self):
        """Create all organization ecosystem relationships in a single complex query."""
        return f"""
        // Match the organization
        MATCH (org:{self.github_org_label} {{id: $org_id}})

        WITH org

        // Create member relationships
        UNWIND COALESCE($members, []) AS member
        OPTIONAL MATCH (user:{self.github_user_label} {{login: member.login}})
        WITH org, COLLECT({{user: user, data: member}}) as member_data
        FOREACH (item IN [i IN member_data WHERE i.user IS NOT NULL] |
            MERGE (item.user)-[member_rel:{self.member_of_rel}]->(org)
            SET member_rel.role = item.data.role,
                member_rel.join_date = item.data.join_date,
                member_rel.state = item.data.state,
                member_rel.visibility = item.data.visibility
        )

        WITH org, member_data

        // Create repository ownership relationships
        UNWIND COALESCE($repositories, []) AS repository
        OPTIONAL MATCH (repo:{self.repository_label} {{id: repository.id}})
        WITH org, member_data, COLLECT({{repo: repo, data: repository}}) as repo_data
        FOREACH (item IN [i IN repo_data WHERE i.repo IS NOT NULL] |
            MERGE (org)-[repo_rel:{self.org_owns_repo_rel}]->(item.repo)
            SET repo_rel.created_at = item.data.created_at
        )

        RETURN org,
               SIZE([i IN member_data WHERE i.user IS NOT NULL]) as member_count,
               SIZE([i IN repo_data WHERE i.repo IS NOT NULL]) as repository_count
        """

class GitHubRepositoryQueries:
    """Queries for GitHub repository management following Google Drive patterns."""

    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.github_org_label = github_schema.get_node_labels()[2]  # GitHubOrganization
        self.branch_label = github_schema.get_node_labels()[7]  # GitHubBranch
        self.contributes_to_rel = github_schema.get_relationship_types()[1]  # CONTRIBUTES_TO
        self.owns_repository_rel = github_schema.get_relationship_types()[0]  # OWNS_REPOSITORY
        self.org_owns_repo_rel = github_schema.get_relationship_types()[5]  # ORG_OWNS_REPO
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.merges_branch_rel = github_schema.get_relationship_types()[41]  # MERGES_BRANCH

    @property
    def CREATE_OR_UPDATE_REPOSITORY(self):
        """Create or update a repository following Google Drive file pattern."""
        return f"""
        MERGE (repo:{self.repository_label} {{id: $id}})
        SET repo.organisation_id = $organisation_id,
            repo.name = $name,
            repo.full_name = $full_name,
            repo.private = $private,
            repo.html_url = $html_url,
            repo.description = $description,
            repo.default_branch = $default_branch,
            repo.stargazers_count = $stargazers_count,
            repo.watchers_count = $watchers_count,
            repo.forks_count = $forks_count,
            repo.open_issues_count = $open_issues_count,
            repo.language = $language,
            repo.created_at = $created_at,
            repo.updated_at = $updated_at,
            repo.pushed_at = $pushed_at
        RETURN repo
        """

    @property
    def CREATE_OR_UPDATE_REPOSITORY_WITH_OWNER(self):
        """Create or update repository with owner relationship."""
        return f"""
        // Create or update the repository
        MERGE (repo:{self.repository_label} {{id: $id}})
        SET repo.organisation_id = $organisation_id,
            repo.name = $name,
            repo.full_name = $full_name,
            repo.private = $private,
            repo.html_url = $html_url,
            repo.description = $description,
            repo.default_branch = $default_branch,
            repo.stargazers_count = $stargazers_count,
            repo.watchers_count = $watchers_count,
            repo.forks_count = $forks_count,
            repo.open_issues_count = $open_issues_count,
            repo.language = $language,
            repo.created_at = $created_at,
            repo.updated_at = $updated_at,
            repo.pushed_at = $pushed_at
        WITH repo
        // Create owner relationship if owner exists
        OPTIONAL MATCH (owner:{self.github_user_label} {{login: $owner_login}})
        FOREACH (o IN CASE WHEN owner IS NOT NULL THEN [owner] ELSE [] END |
            MERGE (o)-[:{self.owns_repository_rel}]->(repo)
        )
        RETURN repo
        """

    @property
    def ADD_REPOSITORY_COLLABORATOR(self):
        """Add a collaborator to a repository with a specific role."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MATCH (gh_user:{self.github_user_label} {{login: $login}})
        MERGE (gh_user)-[r:{self.contributes_to_rel}]->(repo)
        SET r.role = $role,
            r.first_contribution_at = $granted_at,
            r.last_contribution_at = $granted_at
        RETURN r
        """

    @property
    def GET_REPOSITORY_COLLABORATORS(self):
        """Get all collaborators of a repository."""
        return f"""
        MATCH (gh_user:{self.github_user_label})-[r:{self.contributes_to_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN gh_user, r.role as role
        ORDER BY gh_user.login
        """

    @property
    def GET_USER_REPOSITORIES(self):
        """Get all repositories a user has access to."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.contributes_to_rel}]->(repo:{self.repository_label})
        WHERE repo.organisation_id = $organisation_id
        RETURN repo
        ORDER BY repo.name
        """

    @property
    def GET_REPOSITORY_BY_ID(self):
        """Get repository by ID."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        WHERE repo.organisation_id = $organisation_id
        RETURN repo
        """

    @property
    def CREATE_OR_UPDATE_BRANCH(self):
        """Create or update a repository branch."""
        return f"""
        MERGE (branch:GitHubBranch {{name: $name, repository_id: $repository_id}})
        SET branch.organisation_id = $organisation_id,
            branch.last_commit_sha = $last_commit_sha,
            branch.protected = $protected,
            branch.default = $default,
            branch.created_at = $created_at,
            branch.updated_at = $updated_at
        WITH branch
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (branch)-[:{self.belongs_to_rel}]->(repo)
        RETURN branch
        """

    @property
    def CREATE_MERGES_BRANCH_RELATIONSHIP(self):
        """Create MERGES_BRANCH relationship between user and branch."""
        return f"""
        MATCH (user:{self.github_user_label} {{login: $user_login}})
        MATCH (branch:{self.branch_label} {{name: $branch_name, repository_id: $repository_id}})
        MERGE (user)-[r:{self.merges_branch_rel}]->(branch)
        SET r.merged_at = $merged_at,
            r.merge_commit_sha = $merge_commit_sha,
            r.target_branch = $target_branch
        RETURN user, branch
        """

    @property
    def CREATE_COMPREHENSIVE_REPOSITORY_ECOSYSTEM(self):
        """Create all repository ecosystem relationships in a single complex query."""
        return f"""
        // Match the repository
        MATCH (repo:{self.repository_label} {{id: $repository_id}})

        WITH repo

        // Create owner relationship
        OPTIONAL MATCH (owner:{self.github_user_label} {{login: $owner_login}})
        WITH repo, owner
        FOREACH (o IN CASE WHEN owner IS NOT NULL THEN [owner] ELSE [] END |
            MERGE (o)-[owner_rel:{self.owns_repository_rel}]->(repo)
            SET owner_rel.ownership_type = 'owner',
                owner_rel.granted_at = $created_at
        )

        WITH repo, owner

        // Create organization ownership if applicable
        OPTIONAL MATCH (org:{self.github_org_label} {{id: $org_id}})
        WITH repo, owner, org
        FOREACH (g IN CASE WHEN org IS NOT NULL THEN [org] ELSE [] END |
            MERGE (g)-[org_rel:{self.org_owns_repo_rel}]->(repo)
            SET org_rel.created_at = $created_at
        )

        WITH repo, owner, org

        // Create collaborator relationships
        UNWIND COALESCE($collaborators, []) AS collaborator
        OPTIONAL MATCH (collab:{self.github_user_label} {{login: collaborator.login}})
        WITH repo, owner, org, COLLECT({{collab: collab, data: collaborator}}) as collab_data
        FOREACH (item IN [i IN collab_data WHERE i.collab IS NOT NULL] |
            MERGE (item.collab)-[collab_rel:{self.contributes_to_rel}]->(repo)
            SET collab_rel.role = item.data.role,
                collab_rel.granted_at = $created_at,
                collab_rel.permissions_admin = item.data.permissions_admin,
                collab_rel.permissions_push = item.data.permissions_push,
                collab_rel.permissions_pull = item.data.permissions_pull,
                collab_rel.site_admin = item.data.site_admin
        )

        WITH repo, owner, org, collab_data

        // Create fork relationship if applicable
        OPTIONAL MATCH (source_repo:{self.repository_label} {{id: $source_repository_id}})
        WITH repo, owner, org, collab_data, source_repo
        FOREACH (s IN CASE WHEN source_repo IS NOT NULL AND $is_fork = true THEN [source_repo] ELSE [] END |
            MERGE (repo)-[fork_rel:FORKS_FROM]->(s)
            SET fork_rel.forked_at = $created_at
        )

        RETURN repo, owner, org,
               SIZE([i IN collab_data WHERE i.collab IS NOT NULL]) as collaborator_count,
               CASE WHEN $is_fork THEN 1 ELSE 0 END as is_fork_int
        """


class GitHubIssueQueries:
    """Queries for GitHub issues following Google Drive patterns."""

    def __init__(self):
        self.issue_label = github_schema.get_node_labels()[4]  # GitHubIssue
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.creates_issue_rel = github_schema.get_relationship_types()[6]  # CREATES_ISSUE
        self.assigned_to_rel = github_schema.get_relationship_types()[40]  # ASSIGNED_TO

    @property
    def CREATE_OR_UPDATE_ISSUE(self):
        """Create or update a GitHub issue."""
        return f"""
        MERGE (issue:{self.issue_label} {{id: $issue_id}})
        SET issue.organisation_id = $organisation_id,
            issue.number = $number,
            issue.title = $title,
            issue.body = $body,
            issue.state = $state,
            issue.html_url = $html_url,
            issue.created_at = $created_at,
            issue.updated_at = $updated_at,
            issue.closed_at = $closed_at,
            issue.labels = $labels,
            issue.assignees = $assignees,
            issue.milestone_id = $milestone_id,
            issue.milestone_title = $milestone_title,
            issue.milestone_state = $milestone_state,
            issue.comments_count = $comments_count,
            issue.locked = $locked,
            issue.author_association = $author_association
        WITH issue
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (issue)-[:{self.belongs_to_rel}]->(repo)
        RETURN issue
        """

    @property
    def CREATE_ISSUE_CREATOR_RELATIONSHIP(self):
        """Create relationship between issue and its creator."""
        return f"""
        MATCH (issue:{self.issue_label} {{id: $issue_id}})
        MATCH (creator:{self.github_user_label} {{login: $creator_login}})
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (creator)-[r:{self.creates_issue_rel}]->(issue)
        SET r.created_at = $created_at,
            r.repository_id = $repository_id,
            r.repository_name = repo.name
        RETURN r
        """

    @property
    def CREATE_ISSUE_ASSIGNEE_RELATIONSHIP(self):
        """Create relationship between issue and its assignee."""
        return f"""
        MATCH (issue:{self.issue_label} {{id: $issue_id}})
        MATCH (assignee:{self.github_user_label} {{login: $assignee_login}})
        MERGE (assignee)-[r:{self.assigned_to_rel}]->(issue)
        SET r.assigned_at = $assigned_at,
            r.assigned_by = $assigned_by
        RETURN r
        """

    @property
    def GET_REPOSITORY_ISSUES(self):
        """Get all issues for a repository."""
        return f"""
        MATCH (issue:{self.issue_label})-[:{self.belongs_to_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN issue
        ORDER BY issue.number DESC
        """

    @property
    def CREATE_COMPREHENSIVE_ISSUE_RELATIONSHIPS(self):
        """Create all issue relationships in a single complex query."""
        return f"""
        // Match the issue and repository
        MATCH (issue:{self.issue_label} {{id: $issue_id, repository_id: $repository_id}})
        MATCH (repo:{self.repository_label} {{id: $repository_id}})

        // Create BELONGS_TO relationship
        MERGE (issue)-[belongs_rel:{self.belongs_to_rel}]->(repo)
        SET belongs_rel.created_at = $created_at

        WITH issue, repo

        // Create creator relationship
        OPTIONAL MATCH (creator:{self.github_user_label} {{login: $creator_login}})
        WITH issue, repo, creator
        FOREACH (c IN CASE WHEN creator IS NOT NULL THEN [creator] ELSE [] END |
            MERGE (c)-[creator_rel:{self.creates_issue_rel}]->(issue)
            SET creator_rel.created_at = $created_at,
                creator_rel.repository_id = $repository_id,
                creator_rel.repository_name = repo.name
        )

        WITH issue, repo, creator

        // Create assignee relationships
        UNWIND COALESCE($assignee_logins, []) AS assignee_login
        OPTIONAL MATCH (assignee:{self.github_user_label} {{login: assignee_login}})
        WITH issue, repo, creator, COLLECT(assignee) as assignees
        FOREACH (a IN [assignee IN assignees WHERE assignee IS NOT NULL] |
            MERGE (a)-[assign_rel:{self.assigned_to_rel}]->(issue)
            SET assign_rel.assigned_at = $created_at,
                assign_rel.assigned_by = $creator_login
        )

        WITH issue, repo, creator, assignees

        // Create closer relationship if closed
        OPTIONAL MATCH (closer:{self.github_user_label} {{login: $closer_login}})
        WITH issue, repo, creator, assignees, closer
        FOREACH (cl IN CASE WHEN closer IS NOT NULL AND $state = 'closed' THEN [closer] ELSE [] END |
            MERGE (cl)-[close_rel:CLOSES_ISSUE]->(issue)
            SET close_rel.closed_at = $closed_at
        )

        RETURN issue, repo, creator,
               SIZE([a IN assignees WHERE a IS NOT NULL]) as assignee_count
        """


class GitHubPullRequestQueries:
    """Queries for GitHub pull requests following Google Drive patterns."""

    def __init__(self):
        self.pr_label = github_schema.get_node_labels()[5]  # GitHubPullRequest
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.creates_pr_rel = github_schema.get_relationship_types()[10]  # CREATES_PULL_REQUEST
        self.reviews_pr_rel = github_schema.get_relationship_types()[11]  # REVIEWS_PULL_REQUEST

    @property
    def CREATE_OR_UPDATE_PULL_REQUEST(self):
        """Create or update a GitHub pull request."""
        return f"""
        MERGE (pr:{self.pr_label} {{id: $pr_id}})
        SET pr.organisation_id = $organisation_id,
            pr.number = $number,
            pr.title = $title,
            pr.body = $body,
            pr.state = $state,
            pr.html_url = $html_url,
            pr.created_at = $created_at,
            pr.updated_at = $updated_at,
            pr.closed_at = $closed_at,
            pr.merged_at = $merged_at,
            pr.head_ref = $head_ref,
            pr.base_ref = $base_ref,
            pr.draft = $draft,
            pr.labels = $labels,
            pr.assignees = $assignees,
            pr.requested_reviewers = $requested_reviewers,
            pr.milestone_id = $milestone_id,
            pr.milestone_title = $milestone_title,
            pr.milestone_state = $milestone_state,
            pr.comments_count = $comments_count,
            pr.review_comments_count = $review_comments_count,
            pr.commits_count = $commits_count,
            pr.additions = $additions,
            pr.deletions = $deletions,
            pr.changed_files = $changed_files,
            pr.locked = $locked,
            pr.author_association = $author_association,
            pr.mergeable = $mergeable,
            pr.mergeable_state = $mergeable_state,
            pr.rebaseable = $rebaseable
        WITH pr
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (pr)-[:{self.belongs_to_rel}]->(repo)
        RETURN pr
        """

    @property
    def CREATE_PR_CREATOR_RELATIONSHIP(self):
        """Create relationship between pull request and its creator."""
        return f"""
        MATCH (pr:{self.pr_label} {{id: $pr_id}})
        MATCH (creator:{self.github_user_label} {{login: $creator_login}})
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (creator)-[r:{self.creates_pr_rel}]->(pr)
        SET r.created_at = $created_at,
            r.repository_id = $repository_id,
            r.repository_name = repo.name,
            r.head_ref = $head_ref,
            r.base_ref = $base_ref,
            r.draft = $draft
        RETURN r
        """

    @property
    def GET_REPOSITORY_PULL_REQUESTS(self):
        """Get all pull requests for a repository."""
        return f"""
        MATCH (pr:{self.pr_label})-[:{self.belongs_to_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN pr
        ORDER BY pr.number DESC
        """

    @property
    def CREATE_COMPREHENSIVE_PR_RELATIONSHIPS(self):
        """Create all PR relationships in a single complex query."""
        return f"""
        // Match the PR and repository
        MATCH (pr:{self.pr_label} {{id: $pr_id, repository_id: $repository_id}})
        MATCH (repo:{self.repository_label} {{id: $repository_id}})

        // Create BELONGS_TO relationship
        MERGE (pr)-[belongs_rel:{self.belongs_to_rel}]->(repo)
        SET belongs_rel.created_at = $created_at

        WITH pr, repo

        // Create creator relationship
        OPTIONAL MATCH (creator:{self.github_user_label} {{login: $creator_login}})
        WITH pr, repo, creator
        FOREACH (c IN CASE WHEN creator IS NOT NULL THEN [creator] ELSE [] END |
            MERGE (c)-[creator_rel:{self.creates_pr_rel}]->(pr)
            SET creator_rel.created_at = $created_at,
                creator_rel.repository_id = $repository_id,
                creator_rel.repository_name = repo.name,
                creator_rel.head_ref = $head_ref,
                creator_rel.base_ref = $base_ref,
                creator_rel.draft = $draft,
                creator_rel.additions = $additions,
                creator_rel.deletions = $deletions,
                creator_rel.changed_files = $changed_files,
                creator_rel.commits_count = $commits_count,
                creator_rel.mergeable = $mergeable,
                creator_rel.mergeable_state = $mergeable_state
        )

        WITH pr, repo, creator

        // Create assignee relationships
        UNWIND COALESCE($assignee_logins, []) AS assignee_login
        OPTIONAL MATCH (assignee:{self.github_user_label} {{login: assignee_login}})
        WITH pr, repo, creator, COLLECT(assignee) as assignees
        FOREACH (a IN [assignee IN assignees WHERE assignee IS NOT NULL] |
            MERGE (a)-[assign_rel:ASSIGNED_TO]->(pr)
            SET assign_rel.assigned_at = $created_at,
                assign_rel.assigned_by = $creator_login
        )

        WITH pr, repo, creator, assignees

        // Create reviewer relationships
        UNWIND COALESCE($reviewer_logins, []) AS reviewer_login
        OPTIONAL MATCH (reviewer:{self.github_user_label} {{login: reviewer_login}})
        WITH pr, repo, creator, assignees, COLLECT(reviewer) as reviewers
        FOREACH (r IN [reviewer IN reviewers WHERE reviewer IS NOT NULL] |
            MERGE (r)-[review_rel:{self.reviews_pr_rel}]->(pr)
            SET review_rel.reviewed_at = $created_at
        )

        RETURN pr, repo, creator,
               SIZE([a IN assignees WHERE a IS NOT NULL]) as assignee_count,
               SIZE([r IN reviewers WHERE r IS NOT NULL]) as reviewer_count
        """


class GitHubCommitQueries:
    """Queries for GitHub commits following Google Drive patterns."""

    def __init__(self):
        self.commit_label = github_schema.get_node_labels()[6]  # GitHubCommit
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.branch_label = github_schema.get_node_labels()[7]  # GitHubBranch
        self.file_label = github_schema.get_node_labels()[3]  # GitHubCodeFile
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.authors_commit_rel = github_schema.get_relationship_types()[17]  # AUTHORS_COMMIT
        self.commits_to_branch_rel = github_schema.get_relationship_types()[42]  # COMMITS_TO_BRANCH
        self.creates_file_rel = github_schema.get_relationship_types()[43]  # CREATES_FILE
        self.deletes_file_rel = github_schema.get_relationship_types()[44]  # DELETES_FILE

    @property
    def CREATE_OR_UPDATE_COMMIT(self):
        """Create or update a GitHub commit."""
        return f"""
        MERGE (commit:{self.commit_label} {{sha: $sha}})
        SET commit.organisation_id = $organisation_id,
            commit.message = $message,
            commit.html_url = $html_url,
            commit.author_name = $author_name,
            commit.author_email = $author_email,
            commit.committer_name = $committer_name,
            commit.committer_email = $committer_email,
            commit.created_at = $created_at
        WITH commit
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (commit)-[:{self.belongs_to_rel}]->(repo)
        RETURN commit
        """

    @property
    def CREATE_COMMIT_AUTHOR_RELATIONSHIP(self):
        """Create relationship between commit and its author."""
        return f"""
        MATCH (commit:{self.commit_label} {{sha: $sha}})
        MATCH (author:{self.github_user_label} {{login: $author_login}})
        MERGE (author)-[r:{self.authors_commit_rel}]->(commit)
        SET r.authored_at = $authored_at
        RETURN r
        """

    @property
    def GET_REPOSITORY_COMMITS(self):
        """Get all commits for a repository."""
        return f"""
        MATCH (commit:{self.commit_label})-[:{self.belongs_to_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN commit
        ORDER BY commit.created_at DESC
        """

    @property
    def CREATE_COMMIT_REPOSITORY_RELATIONSHIP(self):
        """Create relationship between commit and repository."""
        return f"""
        MATCH (commit:{self.commit_label} {{sha: $sha}})
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (commit)-[:{self.belongs_to_rel}]->(repo)
        SET commit.committed_at = $committed_at
        RETURN commit, repo
        """

    @property
    def CREATE_PARENT_COMMIT_RELATIONSHIP(self):
        """Create PARENT_COMMIT relationship between commits."""
        return f"""
        MATCH (child_commit:{self.commit_label} {{sha: $child_sha, repository_id: $repository_id}})
        MATCH (parent_commit:{self.commit_label} {{sha: $parent_sha, repository_id: $repository_id}})
        MERGE (child_commit)-[r:{github_schema.get_relationship_types()[18]}]->(parent_commit)
        SET r.relationship_type = $relationship_type
        RETURN child_commit, parent_commit
        """

    @property
    def UPDATE_COMMIT_STATS(self):
        """Update commit with detailed statistics."""
        return f"""
        MATCH (commit:{self.commit_label} {{sha: $sha, repository_id: $repository_id}})
        SET commit.additions = $additions,
            commit.deletions = $deletions,
            commit.total_changes = $total_changes
        RETURN commit
        """

    @property
    def CREATE_COMMIT_TO_BRANCH_RELATIONSHIP(self):
        """Create COMMITS_TO_BRANCH relationship between commit and branch."""
        return f"""
        MATCH (commit:{self.commit_label} {{sha: $sha, repository_id: $repository_id}})
        MATCH (branch:{self.branch_label} {{name: $branch_name, repository_id: $repository_id}})
        MERGE (commit)-[r:{self.commits_to_branch_rel}]->(branch)
        SET r.committed_at = $committed_at,
            r.is_merge_commit = $is_merge_commit
        RETURN commit, branch
        """

    @property
    def CREATE_CREATES_FILE_RELATIONSHIP(self):
        """Create CREATES_FILE relationship between commit and file."""
        return f"""
        MATCH (commit:{self.commit_label} {{sha: $sha, repository_id: $repository_id}})
        MATCH (file:{self.file_label} {{path: $file_path, repository_id: $repository_id}})
        MERGE (commit)-[r:{self.creates_file_rel}]->(file)
        SET r.created_at = $created_at,
            r.lines_added = $lines_added
        RETURN commit, file
        """

    @property
    def CREATE_DELETES_FILE_RELATIONSHIP(self):
        """Create DELETES_FILE relationship between commit and file."""
        return f"""
        MATCH (commit:{self.commit_label} {{sha: $sha, repository_id: $repository_id}})
        MATCH (file:{self.file_label} {{path: $file_path, repository_id: $repository_id}})
        MERGE (commit)-[r:{self.deletes_file_rel}]->(file)
        SET r.deleted_at = $deleted_at,
            r.lines_removed = $lines_removed
        RETURN commit, file
        """

    @property
    def CREATE_COMPREHENSIVE_COMMIT_RELATIONSHIPS(self):
        """Create all commit relationships in a single complex query."""
        return f"""
        // Match or create the commit
        MATCH (commit:{self.commit_label} {{sha: $sha, repository_id: $repository_id}})

        // Match repository and create BELONGS_TO relationship
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (commit)-[belongs_rel:{self.belongs_to_rel}]->(repo)
        SET belongs_rel.created_at = $committed_at

        WITH commit, repo

        // Create author relationship if author exists
        OPTIONAL MATCH (author:{self.github_user_label} {{login: $author_login}})
        WITH commit, repo, author
        FOREACH (a IN CASE WHEN author IS NOT NULL THEN [author] ELSE [] END |
            MERGE (a)-[author_rel:{self.authors_commit_rel}]->(commit)
            SET author_rel.authored_at = $authored_at,
                author_rel.committed_at = $committed_at,
                author_rel.additions = $additions,
                author_rel.deletions = $deletions,
                author_rel.total_changes = $total_changes,
                author_rel.files_changed = $files_changed,
                author_rel.verification_verified = $verification_verified
        )

        WITH commit, repo, author

        // Create branch relationship if branch exists
        OPTIONAL MATCH (branch:{self.branch_label} {{name: $branch_name, repository_id: $repository_id}})
        WITH commit, repo, author, branch
        FOREACH (b IN CASE WHEN branch IS NOT NULL THEN [branch] ELSE [] END |
            MERGE (commit)-[branch_rel:{self.commits_to_branch_rel}]->(b)
            SET branch_rel.committed_at = $committed_at,
                branch_rel.is_merge_commit = $is_merge_commit
        )

        WITH commit, repo, author, branch

        // Create parent commit relationships
        UNWIND COALESCE($parent_shas, []) AS parent_sha
        OPTIONAL MATCH (parent_commit:{self.commit_label} {{sha: parent_sha, repository_id: $repository_id}})
        WITH commit, repo, author, branch, COLLECT(parent_commit) as parent_commits
        FOREACH (p IN [pc IN parent_commits WHERE pc IS NOT NULL] |
            MERGE (commit)-[parent_rel:PARENT_COMMIT]->(p)
            SET parent_rel.relationship_type = 'parent'
        )

        RETURN commit, repo, author, branch, SIZE([pc IN parent_commits WHERE pc IS NOT NULL]) as parent_count
        """

    @property
    def CREATE_BATCH_FILE_RELATIONSHIPS(self):
        """Create multiple file relationships for a commit in a single query."""
        return f"""
        // Match the commit
        MATCH (commit:{self.commit_label} {{sha: $sha, repository_id: $repository_id}})

        WITH commit

        // Process file creations
        UNWIND COALESCE($created_files, []) AS created_file
        OPTIONAL MATCH (file:{self.file_label} {{path: created_file.path, repository_id: $repository_id}})
        WITH commit, COLLECT({{file: file, data: created_file}}) as created_file_data
        FOREACH (item IN [i IN created_file_data WHERE i.file IS NOT NULL] |
            MERGE (commit)-[create_rel:{self.creates_file_rel}]->(item.file)
            SET create_rel.created_at = $committed_at,
                create_rel.lines_added = item.data.lines_added
        )

        WITH commit

        // Process file deletions
        UNWIND COALESCE($deleted_files, []) AS deleted_file
        OPTIONAL MATCH (file:{self.file_label} {{path: deleted_file.path, repository_id: $repository_id}})
        WITH commit, COLLECT({{file: file, data: deleted_file}}) as deleted_file_data
        FOREACH (item IN [i IN deleted_file_data WHERE i.file IS NOT NULL] |
            MERGE (commit)-[delete_rel:{self.deletes_file_rel}]->(item.file)
            SET delete_rel.deleted_at = $committed_at,
                delete_rel.lines_removed = item.data.lines_removed
        )

        WITH commit

        // Process file modifications
        UNWIND COALESCE($modified_files, []) AS modified_file
        OPTIONAL MATCH (file:{self.file_label} {{path: modified_file.path, repository_id: $repository_id}})
        WITH commit, COLLECT({{file: file, data: modified_file}}) as modified_file_data
        FOREACH (item IN [i IN modified_file_data WHERE i.file IS NOT NULL] |
            MERGE (commit)-[modify_rel:MODIFIES_FILE]->(item.file)
            SET modify_rel.modification_type = 'modified',
                modify_rel.lines_added = item.data.lines_added,
                modify_rel.lines_removed = item.data.lines_removed
        )

        RETURN commit,
               SIZE(COALESCE($created_files, [])) as files_created,
               SIZE(COALESCE($deleted_files, [])) as files_deleted,
               SIZE(COALESCE($modified_files, [])) as files_modified
        """


class GitHubTeamQueries:
    """Queries for GitHub teams following Google Drive patterns."""

    def __init__(self):
        self.team_label = github_schema.get_node_labels()[9]  # GitHubTeam
        self.github_org_label = github_schema.get_node_labels()[2]  # GitHubOrganization
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.member_of_team_rel = github_schema.get_relationship_types()[34]  # MEMBER_OF_TEAM
        self.team_has_access_rel = github_schema.get_relationship_types()[36]  # TEAM_HAS_ACCESS

    @property
    def CREATE_OR_UPDATE_TEAM(self):
        """Create or update a GitHub team."""
        return f"""
        MERGE (team:{self.team_label} {{id: $team_id}})
        SET team.organisation_id = $organisation_id,
            team.organization_id = $github_organization_id,
            team.name = $name,
            team.slug = $slug,
            team.description = $description,
            team.privacy = $privacy,
            team.permission = $permission,
            team.url = $url,
            team.html_url = $html_url,
            team.members_url = $members_url,
            team.repositories_url = $repositories_url,
            team.parent = $parent,
            team.created_at = $created_at,
            team.updated_at = $updated_at
        WITH team
        MATCH (gh_org:{self.github_org_label} {{id: $github_organization_id}})
        MERGE (team)-[:{self.belongs_to_rel}]->(gh_org)
        RETURN team
        """

    @property
    def ADD_TEAM_MEMBER(self):
        """Add a user to a team."""
        return f"""
        MATCH (team:{self.team_label} {{id: $team_id}})
        MATCH (user:{self.github_user_label} {{login: $login}})
        MERGE (user)-[r:{self.member_of_team_rel}]->(team)
        SET r.role = $role,
            r.joined_at = $joined_at
        RETURN r
        """

    @property
    def ADD_TEAM_REPOSITORY_ACCESS(self):
        """Grant team access to a repository."""
        return f"""
        MATCH (team:{self.team_label} {{id: $team_id}})
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (team)-[r:{self.team_has_access_rel}]->(repo)
        SET r.permission = $permission,
            r.role_name = $role_name,
            r.granted_at = $granted_at
        RETURN r
        """

    @property
    def GET_ORGANIZATION_TEAMS(self):
        """Get all teams for a GitHub organization."""
        return f"""
        MATCH (team:{self.team_label})-[:{self.belongs_to_rel}]->(gh_org:{self.github_org_label} {{id: $github_organization_id}})
        RETURN team
        ORDER BY team.name
        """


class GitHubTagQueries:
    """Queries for GitHub tags following Google Drive patterns."""

    def __init__(self):
        self.tag_label = github_schema.get_node_labels()[10]  # GitHubTag
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.commit_label = github_schema.get_node_labels()[6]  # GitHubCommit
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.points_to_rel = "POINTS_TO"  # Note: POINTS_TO not found in schema, keeping as string

    @property
    def CREATE_OR_UPDATE_TAG(self):
        """Create or update a GitHub tag."""
        return f"""
        MERGE (tag:{self.tag_label} {{name: $name, repository_id: $repository_id}})
        SET tag.organisation_id = $organisation_id,
            tag.sha = $sha,
            tag.commit_sha = $commit_sha,
            tag.message = $message,
            tag.tagger_name = $tagger_name,
            tag.tagger_email = $tagger_email,
            tag.created_at = $created_at
        WITH tag
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (tag)-[:{self.belongs_to_rel}]->(repo)
        RETURN tag
        """

    @property
    def GET_REPOSITORY_TAGS(self):
        """Get all tags for a repository."""
        return f"""
        MATCH (tag:{self.tag_label})-[:{self.belongs_to_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN tag
        ORDER BY tag.created_at DESC
        """

    @property
    def CREATE_TAG_REPOSITORY_RELATIONSHIP(self):
        """Create relationship between tag and repository."""
        return f"""
        MATCH (tag:{self.tag_label} {{name: $tag_name, repository_id: $repository_id}})
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (tag)-[:{self.belongs_to_rel}]->(repo)
        SET tag.created_at = $created_at
        RETURN tag, repo
        """


class GitHubReleaseQueries:
    """Queries for GitHub releases following Google Drive patterns."""

    def __init__(self):
        self.release_label = github_schema.get_node_labels()[11]  # GitHubRelease
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.tag_label = github_schema.get_node_labels()[10]  # GitHubTag
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.based_on_tag_rel = github_schema.get_relationship_types()[34]  # TAGS_RELEASE (closest match)

    @property
    def CREATE_OR_UPDATE_RELEASE(self):
        """Create or update a GitHub release."""
        return f"""
        MERGE (release:{self.release_label} {{id: $release_id}})
        SET release.organisation_id = $organisation_id,
            release.repository_id = $repository_id,
            release.tag_name = $tag_name,
            release.name = $name,
            release.body = $body,
            release.draft = $draft,
            release.prerelease = $prerelease,
            release.html_url = $html_url,
            release.created_at = $created_at,
            release.published_at = $published_at
        WITH release
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (release)-[:{self.belongs_to_rel}]->(repo)
        RETURN release
        """

    @property
    def GET_REPOSITORY_RELEASES(self):
        """Get all releases for a repository."""
        return f"""
        MATCH (release:{self.release_label})-[:{self.belongs_to_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN release
        ORDER BY release.published_at DESC
        """

    @property
    def CREATE_RELEASE_REPOSITORY_RELATIONSHIP(self):
        """Create relationship between release and repository."""
        return f"""
        MATCH (release:{self.release_label} {{id: $release_id}})
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (release)-[:{self.belongs_to_rel}]->(repo)
        SET release.created_at = $created_at
        RETURN release, repo
        """


class GitHubCommentQueries:
    """Queries for GitHub comments following Google Drive patterns."""

    def __init__(self):
        self.comment_label = github_schema.get_node_labels()[12]  # GitHubComment
        self.issue_label = github_schema.get_node_labels()[4]  # GitHubIssue
        self.pr_label = github_schema.get_node_labels()[5]  # GitHubPullRequest
        self.commit_label = github_schema.get_node_labels()[6]  # GitHubCommit
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.comments_on_rel = github_schema.get_relationship_types()[37]  # COMMENTS_ON
        self.creates_comment_rel = "CREATES_COMMENT"  # Use a different relationship for authorship

    @property
    def CREATE_OR_UPDATE_COMMENT(self):
        """Create or update a GitHub comment."""
        return f"""
        MERGE (comment:{self.comment_label} {{id: $comment_id}})
        SET comment.organisation_id = $organisation_id,
            comment.body = $body,
            comment.html_url = $html_url,
            comment.created_at = $created_at,
            comment.updated_at = $updated_at
        WITH comment
        
        // Handle issue relationship if issue_id is provided
        FOREACH (i IN CASE WHEN $issue_id IS NOT NULL THEN [1] ELSE [] END |
            MERGE (issue:{self.issue_label} {{id: $issue_id}})
            SET comment.issue_id = $issue_id
            MERGE (comment)-[:{self.belongs_to_rel}]->(issue)
        )
        
        // Handle pull request relationship if pull_request_id is provided
        FOREACH (p IN CASE WHEN $pull_request_id IS NOT NULL THEN [1] ELSE [] END |
            MERGE (pr:{self.pr_label} {{id: $pull_request_id}})
            SET comment.pull_request_id = $pull_request_id
            MERGE (comment)-[:{self.belongs_to_rel}]->(pr)
        )
        
        RETURN comment
        """

    @property
    def CREATE_COMMENT_AUTHOR_RELATIONSHIP(self):
        """Create relationship between comment and its author."""
        return f"""
        MATCH (comment:{self.comment_label} {{id: $comment_id}})
        MATCH (author:{self.github_user_label} {{login: $author_login}})
        MERGE (author)-[r:{self.creates_comment_rel}]->(comment)
        SET r.created_at = $created_at
        RETURN r
        """

    @property
    def GET_ISSUE_COMMENTS(self):
        """Get all comments for an issue."""
        return f"""
        MATCH (comment:{self.comment_label})-[:{self.belongs_to_rel}]->(issue:{self.issue_label} {{id: $issue_id}})
        RETURN comment
        ORDER BY comment.created_at
        """

    @property
    def GET_PULL_REQUEST_COMMENTS(self):
        """Get all comments for a pull request."""
        return f"""
        MATCH (comment:{self.comment_label})-[:{self.belongs_to_rel}]->(pr:{self.pr_label} {{id: $pull_request_id}})
        RETURN comment
        ORDER BY comment.created_at
        """

    @property
    def CREATE_COMMENTS_ON_ISSUE_RELATIONSHIP(self):
        """Create COMMENTS_ON relationship between user and issue."""
        return f"""
        MATCH (user:{self.github_user_label} {{login: $user_login}})
        MATCH (issue:{self.issue_label} {{id: $issue_id}})
        MERGE (user)-[r:{self.comments_on_rel}]->(issue)
        SET r.commented_at = $commented_at
        RETURN r
        """

    @property
    def CREATE_COMMENTS_ON_PR_RELATIONSHIP(self):
        """Create COMMENTS_ON relationship between user and pull request."""
        return f"""
        MATCH (user:{self.github_user_label} {{login: $user_login}})
        MATCH (pr:{self.pr_label} {{id: $pr_id}})
        MERGE (user)-[r:{self.comments_on_rel}]->(pr)
        SET r.commented_at = $commented_at
        RETURN r
        """

    @property
    def CREATE_COMMENTS_ON_COMMIT_RELATIONSHIP(self):
        """Create COMMENTS_ON relationship between user and commit."""
        return f"""
        MATCH (user:{self.github_user_label} {{login: $user_login}})
        MATCH (commit:{self.commit_label} {{sha: $commit_sha}})
        MERGE (user)-[r:{self.comments_on_rel}]->(commit)
        SET r.commented_at = $commented_at
        RETURN r
        """


class GitHubReviewQueries:
    """Queries for GitHub reviews following Google Drive patterns."""

    def __init__(self):
        self.review_label = github_schema.get_node_labels()[13]  # GitHubReview
        self.pr_label = github_schema.get_node_labels()[5]  # GitHubPullRequest
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.reviews_pr_rel = github_schema.get_relationship_types()[11]  # REVIEWS_PULL_REQUEST

    @property
    def CREATE_OR_UPDATE_REVIEW(self):
        """Create or update a GitHub review."""
        return f"""
        MERGE (review:{self.review_label} {{id: $review_id}})
        SET review.organisation_id = $organisation_id,
            review.pull_request_id = $pull_request_id,
            review.state = $state,
            review.body = $body,
            review.html_url = $html_url,
            review.submitted_at = $submitted_at
        WITH review
        MATCH (pr:{self.pr_label} {{id: $pull_request_id}})
        MERGE (review)-[:{self.belongs_to_rel}]->(pr)
        RETURN review
        """

    @property
    def CREATE_REVIEW_AUTHOR_RELATIONSHIP(self):
        """Create relationship between review and its author."""
        return f"""
        MATCH (review:{self.review_label} {{id: $review_id}})
        MATCH (author:{self.github_user_label} {{login: $author_login}})
        MERGE (author)-[r:{self.reviews_pr_rel}]->(review)
        SET r.submitted_at = $submitted_at
        RETURN r
        """

    @property
    def GET_PULL_REQUEST_REVIEWS(self):
        """Get all reviews for a pull request."""
        return f"""
        MATCH (review:{self.review_label})-[:{self.belongs_to_rel}]->(pr:{self.pr_label} {{id: $pull_request_id}})
        RETURN review
        ORDER BY review.submitted_at
        """


class GitHubWorkflowQueries:
    """Queries for GitHub workflows following Google Drive patterns."""

    def __init__(self):
        self.workflow_label = "GitHubWorkflow"  # Not in schema yet, but needed
        self.workflow_run_label = "GitHubWorkflowRun"  # Not in schema yet, but needed
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO

    @property
    def CREATE_OR_UPDATE_WORKFLOW(self):
        """Create or update a GitHub workflow."""
        return f"""
        MERGE (workflow:{self.workflow_label} {{id: $workflow_id}})
        SET workflow.organisation_id = $organisation_id,
            workflow.repository_id = $repository_id,
            workflow.name = $name,
            workflow.path = $path,
            workflow.state = $state,
            workflow.html_url = $html_url,
            workflow.created_at = $created_at,
            workflow.updated_at = $updated_at
        WITH workflow
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (workflow)-[:{self.belongs_to_rel}]->(repo)
        RETURN workflow
        """

    @property
    def CREATE_OR_UPDATE_WORKFLOW_RUN(self):
        """Create or update a GitHub workflow run."""
        return f"""
        MERGE (run:{self.workflow_run_label} {{id: $run_id}})
        SET run.organisation_id = $organisation_id,
            run.workflow_id = $workflow_id,
            run.repository_id = $repository_id,
            run.head_branch = $head_branch,
            run.head_sha = $head_sha,
            run.status = $status,
            run.conclusion = $conclusion,
            run.html_url = $html_url,
            run.created_at = $created_at,
            run.updated_at = $updated_at,
            run.completed_at = $completed_at
        WITH run
        MATCH (workflow:{self.workflow_label} {{id: $workflow_id}})
        MERGE (run)-[:{self.belongs_to_rel}]->(workflow)
        RETURN run
        """

    @property
    def GET_REPOSITORY_WORKFLOWS(self):
        """Get all workflows for a repository."""
        return f"""
        MATCH (workflow:{self.workflow_label})-[:{self.belongs_to_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN workflow
        ORDER BY workflow.name
        """







class GitHubFileQueries:
    """Queries for GitHub file management following Google Drive patterns."""

    def __init__(self):
        self.file_label = github_schema.get_node_labels()[3]  # GitHubCodeFile
        self.directory_label = github_schema.get_node_labels()[8]  # GitHubDirectory
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.belongs_to_rel = github_schema.get_relationship_types()[29]  # BELONGS_TO
        self.contains_file_rel = github_schema.get_relationship_types()[26]  # CONTAINS_FILE
        self.contains_directory_rel = github_schema.get_relationship_types()[27]  # CONTAINS_DIRECTORY

    @property
    def CREATE_OR_UPDATE_FILE(self):
        """Create or update a code file."""
        return f"""
        MERGE (file:{self.file_label} {{path: $path, repository_id: $repository_id}})
        SET file.organisation_id = $organisation_id,
            file.sha = $sha,
            file.name = $name,
            file.size = $size,
            file.html_url = $html_url,
            file.download_url = $download_url,
            file.content_type = $content_type,
            file.language = $language,
            file.created_at = $created_at,
            file.updated_at = $updated_at
        WITH file
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (file)-[:{self.belongs_to_rel}]->(repo)
        RETURN file
        """

    @property
    def GET_REPOSITORY_FILES(self):
        """Get all files in a repository with pagination."""
        return f"""
        MATCH (file:{self.file_label})-[:{self.belongs_to_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN file
        ORDER BY file.path
        SKIP $skip
        LIMIT $limit
        """

    @property
    def GET_FILE_BY_PATH(self):
        """Get a specific file by path in a repository."""
        return f"""
        MATCH (file:{self.file_label} {{path: $path, repository_id: $repository_id}})
        RETURN file
        """

    @property
    def CREATE_OR_UPDATE_DIRECTORY(self):
        """Create or update a directory."""
        return f"""
        MERGE (dir:{self.directory_label} {{path: $path, repository_id: $repository_id}})
        SET dir.organisation_id = $organisation_id,
            dir.name = $name,
            dir.sha = $sha,
            dir.html_url = $html_url,
            dir.created_at = $created_at,
            dir.updated_at = $updated_at
        WITH dir
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (dir)-[:{self.belongs_to_rel}]->(repo)
        RETURN dir
        """

class GitHubRelationshipQueries:
    """Queries for managing relationships between GitHub entities and organizational entities."""

    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.agent_label = agent_schema.get_node_labels()[0] if agent_schema else "Agent"
        self.department_label = organisation_schema.get_node_labels()[2]  # Department
        self.org_label = organisation_schema.get_node_labels()[0]  # Organisation
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.contributes_to_rel = github_schema.get_relationship_types()[1]  # CONTRIBUTES_TO
        self.belongs_to_rel = organisation_schema.get_relationship_types()[2]  # BELONGS_TO
        self.has_repository_rel = "HAS_REPOSITORY"  # Custom relationship - not in schema

    @property
    def CREATE_ORGANISATION_REPOSITORY_RELATIONSHIP(self):
        """Create organisation-repository relationship."""
        return f"""
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MATCH (org:{self.org_label} {{id: $organisation_id}})
        MERGE (org)-[:{self.has_repository_rel}]->(r)
        """

    @property
    def CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP(self):
        """Create department-repository relationship."""
        return f"""
        MATCH (d:{self.department_label} {{organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (d)-[:{self.contributes_to_rel}]->(r)
        """

    @property
    def MAP_USER_REPOSITORY_ACCESS(self):
        """Map user access to repositories through departments."""
        return f"""
        MATCH (u:{self.user_label} {{organisation_id: $organisation_id}})-[:{self.belongs_to_rel}]->(d:{self.department_label})
        MATCH (d)-[:{self.contributes_to_rel}]->(r:{self.repository_label} {{id: $repository_id}})
        MERGE (u)-[:{self.contributes_to_rel}]->(r)
        """

    @property
    def MAP_GENERAL_DEPARTMENT_ACCESS(self):
        """Ensure GENERAL department has access to the repository."""
        return f"""
        MATCH (d:{self.department_label} {{name: 'GENERAL', organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (d)-[:{self.contributes_to_rel}]->(r)
        """

    @property
    def CHECK_USER_REPOSITORY_ACCESS(self):
        """Check if a user has access to a repository."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id}})
        OPTIONAL MATCH (u)-[:{self.belongs_to_rel}]->(d:{self.department_label})-[:{self.has_access_rel}]->(r)
        OPTIONAL MATCH (gh_user:{self.github_user_label})<-[:HAS_ACCOUNT]-(u)
        OPTIONAL MATCH (gh_user)-[:{self.has_access_rel}]->(r)
        RETURN (d IS NOT NULL OR gh_user IS NOT NULL) as has_access
        """

    @property
    def GET_USER_ORGANISATION(self):
        """Get user's organisation."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.belongs_to_rel}]->(d:{self.department_label})-[:{self.belongs_to_rel}]->(org:Organisation)
        RETURN org.id as organisation_id
        """

    @property
    def CREATE_USER_GITHUB_USER_MAPPING(self):
        """Create mapping between organizational User and GitHubUser following Google Drive pattern."""
        return f"""
        MATCH (u:{self.user_label} {{email: $email, organisation_id: $organisation_id}})
        MATCH (gh_user:{self.github_user_label} {{login: $github_login}})
        MERGE (u)-[:HAS_ACCOUNT]->(gh_user)
        SET gh_user.mapped_at = $mapped_at
        RETURN u, gh_user
        """

    @property
    def CREATE_GITHUB_USER_DEPARTMENT_ACCESS(self):
        """Create GitHub user access through departments following Google Drive pattern."""
        return f"""
        // Map GitHub users to departments through organizational users
        MATCH (u:{self.user_label} {{organisation_id: $organisation_id}})-[:HAS_ACCOUNT]->(gh_user:{self.github_user_label})
        MATCH (u)-[:{self.belongs_to_rel}]->(d:{self.department_label})
        MERGE (gh_user)-[:{self.contributes_to_rel}]->(d)
        RETURN gh_user, d
        """

    @property
    def CREATE_FILE_REPOSITORY_RELATIONSHIP(self):
        """Create relationship between file and repository."""
        return f"""
        MATCH (file:{github_schema.get_node_labels()[3]} {{path: $file_path, repository_id: $repository_id}})
        MATCH (repo:{github_schema.get_node_labels()[0]} {{id: $repository_id}})
        MERGE (file)-[:{github_schema.get_relationship_types()[29]}]->(repo)
        SET file.created_at = $created_at
        RETURN file, repo
        """

    @property
    def CREATE_DIRECTORY_REPOSITORY_RELATIONSHIP(self):
        """Create relationship between directory and repository."""
        return f"""
        MATCH (dir:{github_schema.get_node_labels()[8]} {{path: $directory_path, repository_id: $repository_id}})
        MATCH (repo:{github_schema.get_node_labels()[0]} {{id: $repository_id}})
        MERGE (dir)-[:{github_schema.get_relationship_types()[29]}]->(repo)
        SET dir.created_at = $created_at
        RETURN dir, repo
        """

    @property
    def CREATE_DIRECTORY_CONTAINS_FILE_RELATIONSHIP(self):
        """Create relationship between directory and file it contains."""
        return f"""
        MATCH (dir:{github_schema.get_node_labels()[8]} {{path: $directory_path, repository_id: $repository_id}})
        MATCH (file:{github_schema.get_node_labels()[3]} {{path: $file_path, repository_id: $repository_id}})
        MERGE (dir)-[r:{github_schema.get_relationship_types()[26]}]->(file)
        SET r.added_at = $added_at
        RETURN dir, file
        """

    @property
    def CREATE_DIRECTORY_CONTAINS_DIRECTORY_RELATIONSHIP(self):
        """Create relationship between parent directory and child directory."""
        return f"""
        MATCH (parent_dir:{github_schema.get_node_labels()[8]} {{path: $parent_directory_path, repository_id: $repository_id}})
        MATCH (child_dir:{github_schema.get_node_labels()[8]} {{path: $child_directory_path, repository_id: $repository_id}})
        MERGE (parent_dir)-[r:{github_schema.get_relationship_types()[27]}]->(child_dir)
        SET r.added_at = $added_at
        RETURN parent_dir, child_dir
        """

    @property
    def CREATE_FORK_RELATIONSHIP(self):
        """Create FORKS_FROM relationship between repositories."""
        return f"""
        MATCH (fork_repo:{github_schema.get_node_labels()[0]} {{id: $fork_repository_id}})
        MATCH (source_repo:{github_schema.get_node_labels()[0]} {{id: $source_repository_id}})
        MERGE (fork_repo)-[r:{github_schema.get_relationship_types()[2]}]->(source_repo)
        SET r.forked_at = $forked_at
        RETURN fork_repo, source_repo
        """



    @property
    def CREATE_COMMIT_MODIFIES_FILE_RELATIONSHIP(self):
        """Create MODIFIES_FILE relationship between commit and file."""
        return f"""
        MATCH (commit:{github_schema.get_node_labels()[6]} {{sha: $commit_sha, repository_id: $repository_id}})
        MATCH (file:{github_schema.get_node_labels()[3]} {{path: $file_path, repository_id: $repository_id}})
        MERGE (commit)-[:{github_schema.get_relationship_types()[23]}]->(file)
        SET commit.modification_type = $modification_type,
            commit.lines_added = $lines_added,
            commit.lines_removed = $lines_removed
        RETURN commit, file
        """

    @property
    def CREATE_TAGS_RELEASE_RELATIONSHIP(self):
        """Create TAGS_RELEASE relationship between tag and release."""
        return f"""
        MATCH (tag:{github_schema.get_node_labels()[10]} {{name: $tag_name, repository_id: $repository_id}})
        MATCH (release:{github_schema.get_node_labels()[11]} {{id: $release_id}})
        MERGE (tag)-[:{github_schema.get_relationship_types()[31]}]->(release)
        SET tag.tagged_at = $tagged_at
        RETURN tag, release
        """

    @property
    def CREATE_GITHUB_USER_REPOSITORY_ACCESS(self):
        """Create GitHub user repository access through departments following Google Drive pattern."""
        return f"""
        // Grant repository access to GitHub users through department membership
        MATCH (gh_user:{self.github_user_label})-[:{self.contributes_to_rel}]->(d:{self.department_label})
        MATCH (d)-[:{self.contributes_to_rel}]->(r:{self.repository_label} {{id: $repository_id}})
        WHERE d.organisation_id = $organisation_id
        MERGE (gh_user)-[:{self.contributes_to_rel}]->(r)
        RETURN gh_user, r
        """

    @property
    def CREATE_SHARED_REPOSITORY_ACCESS(self):
        """Create shared repository access relationships following Google Drive pattern."""
        return f"""
        // Create access for users who have been granted repository access
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        WHERE r.shared_with IS NOT NULL
        UNWIND r.shared_with AS login
        MERGE (u:{self.github_user_label} {{login: login, organisation_id: $organisation_id}})
        ON CREATE SET
            u.id = randomUUID(),
            u.created_at = $current_time,
            u.creation_type = 'auto_created'
        ON MATCH SET
            u.creation_type = CASE WHEN u.creation_type IS NULL THEN 'signed_in' ELSE u.creation_type END
        WITH r, u, $current_time as current_time
        WHERE NOT EXISTS {{
           MATCH (u)-[:{self.contributes_to_rel}]->(parent_repo:{self.repository_label})-[:CONTAINS]->(r)
           WHERE parent_repo.organisation_id = r.organisation_id
        }}
        MERGE (u)-[:{self.contributes_to_rel}]->(r)
        SET u.last_access_granted = current_time
        RETURN u, r
        """

class GitHubServiceAccountQueries:
    """Queries for GitHub service account operations."""

    def __init__(self):
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.github_org_label = github_schema.get_node_labels()[2]  # GitHubOrganization

    @property
    def GET_ORGANIZATION_REPOSITORIES(self):
        """Get all repositories for an organization."""
        return f"""
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        RETURN r
        ORDER BY r.name
        """

    @property
    def GET_ORGANIZATION_USERS(self):
        """Get all users for an organization."""
        return f"""
        MATCH (u:{self.github_user_label} {{organisation_id: $organisation_id}})
        RETURN u
        ORDER BY u.login
        """


class GitHubSyncQueries:
    """Queries for GitHub sync operations following Google Drive patterns."""

    def __init__(self):
        self.org_label = organisation_schema.get_node_labels()[0]  # Organisation
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.github_org_label = github_schema.get_node_labels()[2]  # GitHubOrganization
        self.file_label = github_schema.get_node_labels()[3]  # GitHubCodeFile
        self.issue_label = github_schema.get_node_labels()[4]  # GitHubIssue
        self.pr_label = github_schema.get_node_labels()[5]  # GitHubPullRequest
        self.commit_label = github_schema.get_node_labels()[6]  # GitHubCommit
        self.team_label = github_schema.get_node_labels()[9]  # GitHubTeam
        self.tag_label = github_schema.get_node_labels()[10]  # GitHubTag
        self.release_label = github_schema.get_node_labels()[11]  # GitHubRelease
        self.comment_label = github_schema.get_node_labels()[12]  # GitHubComment
        self.review_label = github_schema.get_node_labels()[13]  # GitHubReview

    @property
    def GET_REPOSITORIES_MODIFIED_SINCE(self):
        """Get repositories modified since a specific date."""
        return f"""
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        WHERE r.updated_at > $since_date
        RETURN r
        ORDER BY r.updated_at DESC
        LIMIT $limit
        """

    @property
    def UPDATE_LAST_SYNC_TIME(self):
        """Update the last sync time for an organisation."""
        return """
        MERGE (o:Organisation {id: $organisation_id})-[:HAS_SOURCE]->(s:Source {type: 'github'})
        ON CREATE SET s.last_sync_time = $sync_time, s.created_at = $sync_time
        ON MATCH SET s.last_sync_time = $sync_time, s.updated_at = $sync_time
        RETURN s
        """

    @property
    def GET_SYNC_STATISTICS(self):
        """Get sync statistics for an organisation."""
        return f"""
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (f:{self.file_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (i:{self.issue_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (pr:{self.pr_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (c:{self.commit_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (u:{self.github_user_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (o:Organisation {{id: $organisation_id}})-[:HAS_SOURCE]->(s:Source {{type: 'github'}})
        RETURN
            count(DISTINCT r) as repositories_count,
            count(DISTINCT f) as files_count,
            count(DISTINCT i) as issues_count,
            count(DISTINCT pr) as pull_requests_count,
            count(DISTINCT c) as commits_count,
            count(DISTINCT u) as users_count,
            s.last_sync_time as last_sync_time
        """

class GitHubMetadataQueries:
    """Queries for managing file and repository metadata."""

    def __init__(self):
        self.file_label = github_schema.get_node_labels()[3]  # GitHubCodeFile

    @property
    def UPDATE_FILE_VECTORIZATION_METADATA(self):
        return f"""
        MATCH (f:{self.file_label} {{id: $file_id}})
        SET f.vectorized_at = $vectorized_at,
            f.last_vectorized_modified_time = $modified_time
        """

    @property
    def CHECK_FILE_VECTORIZATION_STATUS(self):
        return f"""
        MATCH (f:{self.file_label} {{id: $file_id}})
        RETURN f.vector_id, f.vectorized_at, f.last_vectorized_modified_time
        """
