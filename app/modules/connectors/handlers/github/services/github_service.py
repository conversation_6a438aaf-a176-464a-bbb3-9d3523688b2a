import json
from datetime import datetime, timedelta
import structlog
from typing import Tu<PERSON>, List, Dict, Any, Optional
import requests
import time
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import base64

from app.modules.connectors.handlers.github.services.github_search_service import GitHubSearchService
from app.services.neo4j_service import execute_write_query, execute_read_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.search.hybrid_search_engine import HybridSearchEngine
from app.utils.source_credentials import get_source_credentials
from app.modules.organisation.repository.source import SourceQueries
from app.utils.constants.sources import SourceType
from app.modules.connectors.handlers.github.models.schema_loader import github_schema
from app.modules.connectors.handlers.github.repository.github_queries import (
    GitHubUserQueries,
    GitHubOrganizationQueries,
    GitHubRepositoryQueries,
    GitHubIssueQueries,
    GitHubPullRequestQueries,
    GitHubCommitQueries,
    GitHubFileQueries,
    GitHubTeamQueries,
    GitHubTagQueries,
    GitHubReleaseQueries,
    GitHubCommentQueries,
    GitHubReviewQueries,
    GitHubRelationshipQueries,
    GitHubSyncQueries
)


logger = structlog.get_logger()


class GitHubAPIClient:
    """
    Dedicated GitHub API client for handling all API interactions.
    Provides clean methods for all GitHub API endpoints with proper error handling,
    rate limiting, and retry logic.
    """
    
    def __init__(self, token: str):
        self.token = token
        self.rate_limit_remaining = 5000
        self.rate_limit_reset_time = None
        self.max_retries = 3
        self.retry_delay = 1
        self.session = self._create_robust_session()
        
    def _create_robust_session(self) -> requests.Session:
        """Create a robust session with retry strategy and rate limiting."""
        session = requests.Session()
        
        # Configure retry strategy with backward compatibility
        retry_kwargs = {
            'total': self.max_retries,
            'status_forcelist': [429, 500, 502, 503, 504],
            'backoff_factor': 2
        }
        
        # Handle both old and new parameter names for urllib3 compatibility
        try:
            retry_strategy = Retry(allowed_methods=["HEAD", "GET", "OPTIONS"], **retry_kwargs)
        except TypeError:
            # Fallback for older urllib3 versions
            retry_strategy = Retry(method_whitelist=["HEAD", "GET", "OPTIONS"], **retry_kwargs)
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set headers
        session.headers.update({
            'Authorization': f'token {self.token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'RuhOrg-GitHub-Connector/1.0',
            'X-GitHub-Api-Version': '2022-11-28'
        })
        
        return session
        
    def _make_paginated_request(self, url: str, params: Dict = None, limit: int = None) -> List[Dict]:
        """Make paginated API requests and return all results."""
        results = []
        page = 1
        per_page = min(100, limit) if limit else 100
        
        while True:
            request_params = params.copy() if params else {}
            request_params.update({'per_page': per_page, 'page': page})
            
            response = self._make_api_request_with_retry(url, request_params)
            if not response or response.status_code != 200:
                break
                
            data = response.json()
            if not data:
                break
                
            results.extend(data)
            
            # Check if we've reached the limit or if there are no more pages
            if limit and len(results) >= limit:
                results = results[:limit]
                break
                
            if len(data) < per_page:
                break
                
            page += 1
            
        return results
        
    def _make_api_request_with_retry(self, url: str, params: Dict = None, method: str = 'GET') -> Optional[requests.Response]:
        """Make API request with comprehensive error handling and retry logic."""
        for attempt in range(self.max_retries + 1):
            try:
                self._check_and_handle_rate_limits()
                
                if method.upper() == 'GET':
                    response = self.session.get(url, params=params, timeout=30)
                elif method.upper() == 'POST':
                    response = self.session.post(url, json=params, timeout=30)
                else:
                    response = self.session.request(method, url, params=params, timeout=30)
                
                self._update_rate_limit_info(response.headers)
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 404:
                    logger.warning(f"Resource not found: {url}")
                    return None
                elif response.status_code == 403:
                    if 'rate limit' in response.text.lower():
                        logger.warning("Rate limit exceeded, waiting...")
                        self._handle_rate_limit_exceeded(response.headers)
                        continue
                    else:
                        logger.error(f"Access forbidden: {url}")
                        return None
                elif response.status_code == 401:
                    logger.error(f"Authentication failed: {url}")
                    return None
                elif response.status_code >= 500:
                    logger.warning(f"Server error {response.status_code}, retrying... (attempt {attempt + 1})")
                    if attempt < self.max_retries:
                        time.sleep(self.retry_delay * (2 ** attempt))
                        continue
                    else:
                        logger.error(f"Server error persisted after {self.max_retries} retries")
                        return None
                else:
                    logger.warning(f"Unexpected status code {response.status_code}: {url}")
                    return None
                    
            except requests.exceptions.Timeout:
                logger.warning(f"Request timeout for {url}, retrying... (attempt {attempt + 1})")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    logger.error(f"Request timeout persisted after {self.max_retries} retries")
                    return None
                    
            except requests.exceptions.ConnectionError:
                logger.warning(f"Connection error for {url}, retrying... (attempt {attempt + 1})")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    logger.error(f"Connection error persisted after {self.max_retries} retries")
                    return None
                    
            except Exception as e:
                logger.error(f"Unexpected error making request to {url}: {str(e)}")
                return None
        
        return None
        
    def _check_and_handle_rate_limits(self):
        """Check rate limits and wait if necessary."""
        if self.rate_limit_remaining <= 10:
            if self.rate_limit_reset_time and datetime.now() < self.rate_limit_reset_time:
                wait_time = (self.rate_limit_reset_time - datetime.now()).total_seconds()
                logger.warning(f"Rate limit low ({self.rate_limit_remaining}), waiting {wait_time:.1f} seconds")
                time.sleep(wait_time + 1)
                
                # Refresh rate limit info
                try:
                    rate_limit_response = self.session.get("https://api.github.com/rate_limit", timeout=10)
                    if rate_limit_response.status_code == 200:
                        rate_data = rate_limit_response.json()
                        self.rate_limit_remaining = rate_data.get('resources', {}).get('core', {}).get('remaining', 5000)
                        reset_timestamp = rate_data.get('resources', {}).get('core', {}).get('reset', 0)
                        self.rate_limit_reset_time = datetime.fromtimestamp(reset_timestamp)
                except Exception as e:
                    logger.warning(f"Failed to refresh rate limit info: {str(e)}")

    def _update_rate_limit_info(self, headers: Dict[str, str]):
        """Update rate limit information from response headers."""
        try:
            if 'X-RateLimit-Remaining' in headers:
                self.rate_limit_remaining = int(headers['X-RateLimit-Remaining'])
            if 'X-RateLimit-Reset' in headers:
                reset_timestamp = int(headers['X-RateLimit-Reset'])
                self.rate_limit_reset_time = datetime.fromtimestamp(reset_timestamp)
                
            logger.debug(f"Rate limit - Remaining: {self.rate_limit_remaining}, Reset: {self.rate_limit_reset_time}")
        except (ValueError, KeyError) as e:
            logger.debug(f"Could not parse rate limit headers: {str(e)}")

    def _handle_rate_limit_exceeded(self, headers: Dict[str, str]):
        """Handle rate limit exceeded scenario."""
        try:
            if 'X-RateLimit-Reset' in headers:
                reset_timestamp = int(headers['X-RateLimit-Reset'])
                reset_time = datetime.fromtimestamp(reset_timestamp)
                wait_time = (reset_time - datetime.now()).total_seconds()
                
                if wait_time > 0:
                    logger.warning(f"Rate limit exceeded, waiting {wait_time:.1f} seconds until reset")
                    time.sleep(wait_time + 5)  # Add 5 seconds buffer
                    self.rate_limit_remaining = 5000  # Reset to default
                    self.rate_limit_reset_time = reset_time
        except Exception as e:
            logger.warning(f"Error handling rate limit: {str(e)}, waiting 60 seconds")
            time.sleep(60)

    def _safe_api_call(self, url: str, params: Dict = None, method: str = 'GET', context: str = "API call") -> Optional[Dict]:
        """Safely make API call with comprehensive error handling."""
        try:
            response = self._make_api_request_with_retry(url, params, method)
            if response and response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed {context}: {url}")
                return None
        except Exception as e:
            logger.error(f"Error in {context}: {str(e)}")
            return None
            
    def test_authentication(self) -> Optional[Dict]:
        """Test GitHub authentication."""
        return self._safe_api_call("https://api.github.com/user", context="authentication test")
        
    # User API methods
    def get_authenticated_user(self) -> Optional[Dict]:
        """Get the authenticated user's profile."""
        return self._safe_api_call("https://api.github.com/user", context="get authenticated user")
        
    def get_user_organizations(self) -> List[Dict]:
        """Get organizations for the authenticated user."""
        return self._make_paginated_request("https://api.github.com/user/orgs")
        
    def get_user_repositories(self, limit: int = None) -> List[Dict]:
        """Get repositories for the authenticated user."""
        params = {'type': 'all', 'sort': 'updated'}
        return self._make_paginated_request("https://api.github.com/user/repos", params, limit)

    # Organization API methods
    def get_organization(self, org_login: str) -> Optional[Dict]:
        """Get organization details."""
        return self._safe_api_call(f"https://api.github.com/orgs/{org_login}", context=f"get organization {org_login}")

    def get_organization_members(self, org_login: str, limit: int = None) -> List[Dict]:
        """Get all members of an organization."""
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/members", limit=limit)

    def get_organization_member_details(self, org_login: str, username: str) -> Optional[Dict]:
        """Get detailed membership information for a specific user in an organization."""
        return self._safe_api_call(f"https://api.github.com/orgs/{org_login}/memberships/{username}",
                                 context=f"get member details {org_login}/{username}")

    def get_organization_teams(self, org_login: str, limit: int = None) -> List[Dict]:
        """Get all teams of an organization."""
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/teams", limit=limit)

    def get_organization_repositories(self, org_login: str, limit: int = None) -> List[Dict]:
        """Get all repositories of an organization."""
        params = {'type': 'all', 'sort': 'updated'}
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/repos", params, limit)

    def get_team_members(self, org_login: str, team_slug: str, limit: int = None) -> List[Dict]:
        """Get members of a specific team."""
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/members", limit=limit)

    def get_team_member_details(self, org_login: str, team_slug: str, username: str) -> Optional[Dict]:
        """Get detailed membership information for a specific user in a team."""
        return self._safe_api_call(f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/memberships/{username}",
                                 context=f"get team member details {org_login}/{team_slug}/{username}")

    def get_team_repositories(self, org_login: str, team_slug: str, limit: int = None) -> List[Dict]:
        """Get repositories accessible by a specific team."""
        return self._make_paginated_request(f"https://api.github.com/orgs/{org_login}/teams/{team_slug}/repos", limit=limit)

    # Repository API methods
    def get_repository(self, owner: str, repo: str) -> Optional[Dict]:
        """Get repository details."""
        return self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}", context=f"get repository {owner}/{repo}")

    def get_repository_issues(self, owner: str, repo: str, state: str = 'all', limit: int = None) -> List[Dict]:
        """Get repository issues."""
        params = {'state': state}
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/issues", params, limit)

    def get_repository_pull_requests(self, owner: str, repo: str, state: str = 'all', limit: int = None) -> List[Dict]:
        """Get repository pull requests."""
        params = {'state': state}
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/pulls", params, limit)

    def get_repository_commits(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get repository commits."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/commits", limit=limit)

    def get_repository_branches(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get repository branches."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/branches", limit=limit)

    def get_repository_tags(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get repository tags."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/tags", limit=limit)

    def get_repository_releases(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get repository releases."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/releases", limit=limit)

    def get_repository_contents(self, owner: str, repo: str, path: str = "") -> List[Dict]:
        """Get repository file structure."""
        result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/contents/{path}",
                                   context=f"get repository {owner}/{repo} contents")
        return result if isinstance(result, list) else [result] if result else []

    def get_file_content(self, owner: str, repo: str, path: str) -> Optional[str]:
        """Get file content from repository."""
        result = self._safe_api_call(f"https://api.github.com/repos/{owner}/{repo}/contents/{path}",
                                   context=f"get file content {owner}/{repo}/{path}")
        if result and result.get('content'):
            try:
                return base64.b64decode(result['content']).decode('utf-8')
            except Exception as e:
                logger.warning(f"Failed to decode file content: {str(e)}")
                return None
        return None

    def get_repository_tree(self, owner: str, repo: str, tree_sha: str, recursive: bool = True) -> Optional[Dict]:
        """
        Get repository tree structure using the Git Trees API.
        This is much more efficient than recursively calling get_repository_contents.

        Args:
            owner: Repository owner
            repo: Repository name
            tree_sha: SHA of the tree (usually the default branch)
            recursive: Whether to get the tree recursively (includes all subdirectories)

        Returns:
            Dict containing the tree structure or None if failed
        """
        params = {'recursive': '1'} if recursive else {}
        return self._safe_api_call(
            f"https://api.github.com/repos/{owner}/{repo}/git/trees/{tree_sha}",
            params=params,
            context=f"get repository {owner}/{repo} tree"
        )

    def get_commit_details(self, owner: str, repo: str, commit_sha: str) -> Optional[Dict]:
        """
        Get detailed commit information including file changes.

        Args:
            owner: Repository owner
            repo: Repository name
            commit_sha: SHA of the commit

        Returns:
            Dict containing detailed commit information or None if failed
        """
        return self._safe_api_call(
            f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_sha}",
            context=f"get commit details {owner}/{repo}/{commit_sha}"
        )

    def get_issue_comments(self, owner: str, repo: str, issue_number: int, limit: int = None) -> List[Dict]:
        """Get comments for a specific issue."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/issues/{issue_number}/comments", limit=limit)

    def get_pr_reviews(self, owner: str, repo: str, pr_number: int, limit: int = None) -> List[Dict]:
        """Get reviews for a specific pull request."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/reviews", limit=limit)

    def get_pr_review_comments(self, owner: str, repo: str, pr_number: int, limit: int = None) -> List[Dict]:
        """Get review comments for a specific pull request."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/comments", limit=limit)

    def get_repository_collaborators(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get collaborators for a repository."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/collaborators", limit=limit)

    def get_repository_contributors(self, owner: str, repo: str, limit: int = None) -> List[Dict]:
        """Get contributors for a repository."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/contributors", limit=limit)

    def get_commit_comments(self, owner: str, repo: str, commit_sha: str, limit: int = None) -> List[Dict]:
        """Get comments for a specific commit."""
        return self._make_paginated_request(f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_sha}/comments", limit=limit)


class GitHubEntityMapper:
    """
    Maps raw GitHub API responses to standardized schema entities.
    Handles data normalization, property mapping, and ensures all entities
    conform to the github_schema.yml definitions.
    """

    def __init__(self):
        pass

    def map_user(self, user_data: Dict, organisation_id: str) -> Dict:
        """
        Map GitHub user data to schema format.

        Args:
            user_data: GitHub user data from API
            organisation_id: Main system organization ID (for scoping)
        """
        return {
            'id': user_data.get('id'),  # GitHub user ID (matches schema)
            'organisation_id': organisation_id,  # Main system organization ID
            'login': user_data.get('login'),
            'name': user_data.get('name'),
            'email': user_data.get('email'),
            'html_url': user_data.get('html_url'),
            'type': user_data.get('type', 'User'),
            'created_at': user_data.get('created_at'),
            'updated_at': user_data.get('updated_at')
        }

    def map_organization(self, org_data: Dict, organisation_id: str) -> Dict:
        """
        Map GitHub organization data to schema format.

        Args:
            org_data: GitHub organization data from API
            organisation_id: Main system organization ID (not GitHub org ID)
        """
        return {
            'id': org_data.get('id'),  # GitHub organization ID (matches schema)
            'organisation_id': organisation_id,  # Main system organization ID
            'login': org_data.get('login'),
            'name': org_data.get('name'),
            'description': org_data.get('description'),
            'html_url': org_data.get('html_url'),
            'public_repos': org_data.get('public_repos', 0),
            'public_gists': org_data.get('public_gists', 0),
            'followers': org_data.get('followers', 0),
            'following': org_data.get('following', 0),
            'created_at': org_data.get('created_at'),
            'updated_at': org_data.get('updated_at')
        }

    def map_repository(self, repo_data: Dict, organisation_id: str) -> Dict:
        """
        Map GitHub repository data to schema format.

        Args:
            repo_data: GitHub repository data from API
            organisation_id: Main system organization ID (for scoping)
        """
        # Extract languages - GitHub API provides primary language, we'll store it as both single and array
        primary_language = repo_data.get('language')
        languages = [primary_language] if primary_language else []

        return {
            'id': repo_data.get('id'),  # GitHub repository ID (matches query parameter)
            'organisation_id': organisation_id,  # Main system organization ID
            'name': repo_data.get('name'),
            'full_name': repo_data.get('full_name'),
            'description': repo_data.get('description'),
            'private': repo_data.get('private', False),
            'html_url': repo_data.get('html_url'),
            'default_branch': repo_data.get('default_branch'),
            'stargazers_count': repo_data.get('stargazers_count', 0),
            'watchers_count': repo_data.get('watchers_count', 0),
            'forks_count': repo_data.get('forks_count', 0),
            'open_issues_count': repo_data.get('open_issues_count', 0),
            'language': primary_language,  # Primary language
            'languages': languages,  # Array of languages
            'archived': repo_data.get('archived', False),
            'pushed_at': repo_data.get('pushed_at'),  # Add missing pushed_at parameter
            'created_at': repo_data.get('created_at'),
            'updated_at': repo_data.get('updated_at')
        }

    def map_issue(self, issue_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub issue data to schema format with enriched properties."""
        # Extract labels from issue data
        labels = []
        if issue_data.get('labels'):
            labels = [label.get('name') for label in issue_data.get('labels', []) if label.get('name')]

        # Extract assignees
        assignees = []
        if issue_data.get('assignees'):
            assignees = [assignee.get('login') for assignee in issue_data.get('assignees', []) if assignee.get('login')]

        # Extract milestone information
        milestone = None
        milestone_title = None
        milestone_state = None
        if issue_data.get('milestone'):
            milestone = issue_data['milestone'].get('id')
            milestone_title = issue_data['milestone'].get('title')
            milestone_state = issue_data['milestone'].get('state')

        return {
            'issue_id': issue_data.get('id'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'number': issue_data.get('number'),
            'title': issue_data.get('title'),
            'body': issue_data.get('body'),
            'state': issue_data.get('state'),
            'html_url': issue_data.get('html_url'),
            'created_at': issue_data.get('created_at'),
            'updated_at': issue_data.get('updated_at'),
            'closed_at': issue_data.get('closed_at'),
            'labels': labels,
            'assignees': assignees,
            'milestone_id': milestone,
            'milestone_title': milestone_title,
            'milestone_state': milestone_state,
            'comments_count': issue_data.get('comments', 0),
            'locked': issue_data.get('locked', False),
            'author_association': issue_data.get('author_association')
        }

    def map_pull_request(self, pr_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub pull request data to schema format with enriched properties."""
        # Extract labels from PR data
        labels = []
        if pr_data.get('labels'):
            labels = [label.get('name') for label in pr_data.get('labels', []) if label.get('name')]

        # Extract assignees
        assignees = []
        if pr_data.get('assignees'):
            assignees = [assignee.get('login') for assignee in pr_data.get('assignees', []) if assignee.get('login')]

        # Extract requested reviewers
        requested_reviewers = []
        if pr_data.get('requested_reviewers'):
            requested_reviewers = [reviewer.get('login') for reviewer in pr_data.get('requested_reviewers', []) if reviewer.get('login')]

        # Extract milestone information
        milestone = None
        milestone_title = None
        milestone_state = None
        if pr_data.get('milestone'):
            milestone = pr_data['milestone'].get('id')
            milestone_title = pr_data['milestone'].get('title')
            milestone_state = pr_data['milestone'].get('state')

        return {
            'pr_id': pr_data.get('id'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'number': pr_data.get('number'),
            'title': pr_data.get('title'),
            'body': pr_data.get('body'),
            'state': pr_data.get('state'),
            'html_url': pr_data.get('html_url'),
            'head_ref': pr_data.get('head', {}).get('ref'),  # Fixed: changed from head_branch to head_ref
            'base_ref': pr_data.get('base', {}).get('ref'),  # Fixed: changed from base_branch to base_ref
            'merged': pr_data.get('merged', False),
            'draft': pr_data.get('draft', False),  # Added: missing draft parameter
            'created_at': pr_data.get('created_at'),
            'updated_at': pr_data.get('updated_at'),
            'closed_at': pr_data.get('closed_at'),  # Added: missing closed_at parameter
            'merged_at': pr_data.get('merged_at'),
            'labels': labels,
            'assignees': assignees,
            'requested_reviewers': requested_reviewers,
            'milestone_id': milestone,
            'milestone_title': milestone_title,
            'milestone_state': milestone_state,
            'comments_count': pr_data.get('comments', 0),
            'review_comments_count': pr_data.get('review_comments', 0),
            'commits_count': pr_data.get('commits', 0),
            'additions': pr_data.get('additions', 0),
            'deletions': pr_data.get('deletions', 0),
            'changed_files': pr_data.get('changed_files', 0),
            'locked': pr_data.get('locked', False),
            'author_association': pr_data.get('author_association'),
            'mergeable': pr_data.get('mergeable'),
            'mergeable_state': pr_data.get('mergeable_state'),
            'rebaseable': pr_data.get('rebaseable')
        }

    def map_commit(self, commit_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub commit data to schema format."""
        commit_info = commit_data.get('commit', {})
        author_info = commit_info.get('author', {})
        committer_info = commit_info.get('committer', {})

        return {
            'sha': commit_data.get('sha'),
            'organisation_id': organisation_id,
            'repository_id': repository_id,
            'message': commit_info.get('message'),
            'author_name': author_info.get('name'),
            'author_email': author_info.get('email'),
            'committer_name': committer_info.get('name'),
            'committer_email': committer_info.get('email'),
            'html_url': commit_data.get('html_url'),
            'date': author_info.get('date'),
            'created_at': author_info.get('date')
        }

    def map_team(self, team_data: Dict, organisation_id: str, github_organization_id: int) -> Dict:
        """
        Map GitHub team data to schema format.

        Args:
            team_data: GitHub team data from API
            organisation_id: Main system organization ID (for scoping)
            github_organization_id: GitHub organization ID (for GitHub-specific relationships)
        """
        # Construct proper members_url without template parameters
        members_url = team_data.get('members_url', '')
        if '{/member}' in members_url:
            members_url = members_url.replace('{/member}', '')

        # Construct proper repositories_url without template parameters
        repositories_url = team_data.get('repositories_url', '')
        if '{/repository}' in repositories_url:
            repositories_url = repositories_url.replace('{/repository}', '')

        return {
            'team_id': team_data.get('id'),  # GitHub team ID (matches query parameter)
            'organisation_id': organisation_id,  # Main system organization ID
            'github_organization_id': github_organization_id,  # GitHub organization ID (matches query parameter)
            'name': team_data.get('name'),
            'slug': team_data.get('slug'),
            'description': team_data.get('description'),
            'privacy': team_data.get('privacy'),
            'permission': team_data.get('permission'),
            'url': team_data.get('url'),
            'html_url': team_data.get('html_url'),
            'members_url': members_url,
            'repositories_url': repositories_url,
            'parent': team_data.get('parent'),
            'created_at': team_data.get('created_at'),
            'updated_at': team_data.get('updated_at')
        }

    def map_file(self, file_data: Dict, organisation_id: str, repository_id: int, owner: str = None, repo_name: str = None) -> Dict:
        """
        Map GitHub file data from Git Trees API to schema format.

        Args:
            file_data: File data from GitHub Git Trees API
            organisation_id: Main system organization ID
            repository_id: Repository ID
            owner: Repository owner (for URL construction)
            repo_name: Repository name (for URL construction)
        """
        # Construct proper GitHub URL using owner/repo instead of repository_id
        html_url = None
        if file_data.get('path') and owner and repo_name:
            html_url = f"https://github.com/{owner}/{repo_name}/blob/main/{file_data.get('path', '')}"

        # Determine languages for the file
        primary_language = self._determine_language(file_data.get('path', ''))
        languages = [primary_language] if primary_language else []

        return {
            'path': file_data.get('path'),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'sha': file_data.get('sha'),
            'name': file_data.get('path', '').split('/')[-1] if file_data.get('path') else '',
            'size': file_data.get('size', 0),
            'html_url': html_url,
            'download_url': file_data.get('url'),  # This is the API URL, not download URL
            'content_type': self._determine_content_type(file_data.get('path', '')),
            'language': primary_language,  # Primary language
            'languages': languages,  # Array of languages
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

    def map_directory(self, dir_data: Dict, organisation_id: str, repository_id: int, owner: str = None, repo_name: str = None) -> Dict:
        """
        Map GitHub directory data from Git Trees API to schema format.

        Args:
            dir_data: Directory data from GitHub Git Trees API
            organisation_id: Main system organization ID
            repository_id: Repository ID
            owner: Repository owner (for URL construction)
            repo_name: Repository name (for URL construction)
        """
        # Construct proper GitHub URL using owner/repo instead of repository_id
        html_url = None
        if dir_data.get('path') and owner and repo_name:
            html_url = f"https://github.com/{owner}/{repo_name}/tree/main/{dir_data.get('path', '')}"

        return {
            'path': dir_data.get('path'),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'name': dir_data.get('path', '').split('/')[-1] if dir_data.get('path') else '',
            'sha': dir_data.get('sha'),
            'html_url': html_url,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

    def map_tag(self, tag_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub tag data to schema format."""
        return {
            'name': tag_data.get('name'),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'sha': tag_data.get('commit', {}).get('sha'),
            'message': tag_data.get('message', ''),
            'tagger_name': tag_data.get('tagger', {}).get('name') if tag_data.get('tagger') else None,
            'tagger_email': tag_data.get('tagger', {}).get('email') if tag_data.get('tagger') else None,
            'created_at': tag_data.get('tagger', {}).get('date') if tag_data.get('tagger') else datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

    def map_release(self, release_data: Dict, organisation_id: str, repository_id: int) -> Dict:
        """Map GitHub release data to schema format."""
        return {
            'release_id': release_data.get('id'),
            'repository_id': repository_id,
            'organisation_id': organisation_id,
            'tag_name': release_data.get('tag_name'),
            'name': release_data.get('name'),
            'body': release_data.get('body'),
            'draft': release_data.get('draft', False),
            'prerelease': release_data.get('prerelease', False),
            'html_url': release_data.get('html_url'),
            'tarball_url': release_data.get('tarball_url'),
            'zipball_url': release_data.get('zipball_url'),
            'created_at': release_data.get('created_at'),
            'published_at': release_data.get('published_at'),
            'updated_at': release_data.get('updated_at')
        }

    def _determine_content_type(self, file_path: str) -> str:
        """Determine content type based on file extension."""
        if not file_path:
            return 'unknown'

        file_path = file_path.lower()

        # Code files
        code_extensions = ['.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt']
        if any(file_path.endswith(ext) for ext in code_extensions):
            return 'code'

        # Documentation
        doc_extensions = ['.md', '.txt', '.rst', '.adoc', '.tex']
        if any(file_path.endswith(ext) for ext in doc_extensions):
            return 'documentation'

        # Configuration
        config_extensions = ['.json', '.yaml', '.yml', '.xml', '.toml', '.ini', '.cfg', '.conf']
        config_files = ['dockerfile', 'makefile', 'rakefile', 'gemfile', 'requirements.txt', 'package.json', 'composer.json']
        if any(file_path.endswith(ext) for ext in config_extensions) or any(name in file_path for name in config_files):
            return 'config'

        # Data files
        data_extensions = ['.csv', '.sql', '.db', '.sqlite']
        if any(file_path.endswith(ext) for ext in data_extensions):
            return 'data'

        return 'other'

    def _determine_language(self, file_path: str) -> str:
        """Determine programming language based on file extension."""
        if not file_path:
            return None

        file_path = file_path.lower()

        language_map = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C',
            '.h': 'C',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.swift': 'Swift',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.sh': 'Shell',
            '.bash': 'Shell',
            '.zsh': 'Shell',
            '.fish': 'Shell'
        }

        for ext, lang in language_map.items():
            if file_path.endswith(ext):
                return lang

        return None


class GitHubSyncService:
    """
    Orchestrates the complete GitHub data fetching and storage process.
    Provides clean separation of concerns with methods for organization-level
    and user-level synchronization following Google Drive patterns.
    """

    def __init__(self, api_client: GitHubAPIClient, entity_mapper: GitHubEntityMapper):
        self.api_client = api_client
        self.entity_mapper = entity_mapper

        # Initialize query instances (following Google Drive pattern)
        self.user_queries = GitHubUserQueries()
        self.organization_queries = GitHubOrganizationQueries()
        self.repository_queries = GitHubRepositoryQueries()
        self.issue_queries = GitHubIssueQueries()
        self.pull_request_queries = GitHubPullRequestQueries()
        self.commit_queries = GitHubCommitQueries()
        self.file_queries = GitHubFileQueries()
        self.team_queries = GitHubTeamQueries()
        self.tag_queries = GitHubTagQueries()
        self.release_queries = GitHubReleaseQueries()
        self.comment_queries = GitHubCommentQueries()
        self.review_queries = GitHubReviewQueries()
        self.relationship_queries = GitHubRelationshipQueries()
        self.sync_queries = GitHubSyncQueries()

    def sync_organization(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync GitHub organization data following Google Drive patterns.

        Args:
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            logger.info(f"Starting GitHub organization sync for organisation: {organisation_id}")

            # Test authentication
            auth_test = self.api_client.test_authentication()
            if not auth_test:
                return False, "GitHub authentication failed", 0

            logger.info(f"Authenticated as GitHub user: {auth_test.get('login', 'unknown')}")

            items_synced = 0

            # 1. Fetch and sync user organizations
            organizations = self.api_client.get_user_organizations()
            for org_data in organizations:
                # Create organization node
                org_entity = self.entity_mapper.map_organization(org_data, organisation_id)
                self._create_or_update_entity(
                    self.organization_queries.CREATE_OR_UPDATE_GITHUB_ORGANIZATION,
                    org_entity,
                )
                items_synced += 1

                # Sync organization details
                org_details = self.api_client.get_organization(org_data['login'])
                if org_details:
                    org_entity = self.entity_mapper.map_organization(org_details, organisation_id)
                    self._create_or_update_entity(self.organization_queries.CREATE_OR_UPDATE_GITHUB_ORGANIZATION, org_entity)

                # 2. Sync organization members
                members = self.api_client.get_organization_members(org_data['login'])
                for member_data in members:
                    user_entity = self.entity_mapper.map_user(member_data, organisation_id)
                    self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, user_entity)

                    # Get detailed membership information for role and join date
                    member_details = self.api_client.get_organization_member_details(org_data['login'], user_entity['login'])
                    role = 'member'  # default
                    join_date = datetime.utcnow().isoformat()  # fallback

                    if member_details:
                        role = member_details.get('role', 'member')  # 'admin' or 'member'
                        # GitHub doesn't provide join date in membership API, so we use creation time as fallback
                        join_date = member_details.get('created_at', datetime.utcnow().isoformat())

                    self._create_relationship(self.organization_queries.ADD_MEMBER_TO_ORGANIZATION, {
                        'login': user_entity['login'],
                        'github_org_id': org_entity['id'],  # Fixed: use 'id' instead of 'github_org_id'
                        'role': role,
                        'join_date': join_date
                    })
                    items_synced += 1

                # 3. Sync organization teams
                teams = self.api_client.get_organization_teams(org_data['login'])
                print(len(teams))
                for team_data in teams:
                    team_entity = self.entity_mapper.map_team(team_data, organisation_id, org_entity['id'])  # Fixed: use 'id' instead of 'github_org_id'
                    self._create_or_update_entity(self.team_queries.CREATE_OR_UPDATE_TEAM, team_entity)
                    items_synced += 1

                    # Sync team members
                    team_members = self.api_client.get_team_members(org_data['login'], team_data['slug'])
                    for member_data in team_members:
                        user_entity = self.entity_mapper.map_user(member_data, organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, user_entity)

                        # Get detailed team membership information for role
                        member_details = self.api_client.get_team_member_details(org_data['login'], team_data['slug'], user_entity['login'])
                        role = 'member'  # default
                        joined_at = datetime.utcnow().isoformat()  # fallback

                        if member_details:
                            role = member_details.get('role', 'member')  # 'maintainer' or 'member'
                            # GitHub doesn't provide join date in team membership API, so we use creation time as fallback
                            joined_at = member_details.get('created_at', datetime.utcnow().isoformat())

                        self._create_relationship(self.team_queries.ADD_TEAM_MEMBER, {
                                'login': user_entity['login'],
                                'team_id': team_entity['team_id'],  # Fixed: use 'team_id' to match map_team output
                                'role': role,
                                'joined_at': joined_at
                            })
                        items_synced += 1

                    # Sync team repository access
                    team_repos = self.api_client.get_team_repositories(org_data['login'], team_data['slug'])
                    for repo_data in team_repos:
                        repo_entity = self.entity_mapper.map_repository(repo_data, organisation_id)
                        # Ensure repository exists first
                        self._create_or_update_entity(self.repository_queries.CREATE_OR_UPDATE_REPOSITORY, repo_entity)

                        # Create team repository access relationship
                        self._create_relationship(self.team_queries.ADD_TEAM_REPOSITORY_ACCESS, {
                            'team_id': team_entity['team_id'],  # Fixed: use 'team_id' to match map_team output
                            'repository_id': repo_entity['id'],  # Fixed: use 'id' instead of 'repository_id'
                            'permission': repo_data.get('permissions', {}).get('admin', False) and 'admin' or
                                        repo_data.get('permissions', {}).get('push', False) and 'write' or 'read',
                            'role_name': repo_data.get('role_name', 'read'),  # Add role_name from team_repos data
                            'granted_at': datetime.utcnow().isoformat()
                        })
                        items_synced += 1

                # 4. Sync organization repositories
                repositories = self.api_client.get_organization_repositories(org_data['login'])
                print("repositories", repositories)
                for repo_data in repositories:
                    repo_items = self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)
                    items_synced += repo_items

                    # Create organization-repository ownership relationship
                    repo_entity = self.entity_mapper.map_repository(repo_data, organisation_id)
                    self._create_relationship(self.organization_queries.ADD_REPOSITORY_TO_ORGANIZATION, {
                        'repository_id': repo_entity['id'],  # Fixed: use 'id' instead of 'repository_id'
                        'github_org_id': org_entity['id'],  # Fixed: use 'id' instead of 'github_org_id'
                        'created_at': repo_data.get('created_at')
                    })

            logger.info(f"Successfully completed GitHub organization sync: {items_synced} items")
            return True, f"Successfully synced GitHub organizations", items_synced

        except Exception as e:
            logger.error(f"Error in GitHub organization sync: {str(e)}")
            return False, f"Organization sync failed: {str(e)}", 0

    def sync_user(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync GitHub user data following Google Drive patterns.

        Args:
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            logger.info(f"Starting GitHub user sync for organisation: {organisation_id}")

            # Test authentication
            auth_test = self.api_client.test_authentication()
            if not auth_test:
                return False, "GitHub authentication failed", 0

            logger.info(f"Authenticated as GitHub user: {auth_test.get('login', 'unknown')}")

            items_synced = 0

            # 1. Sync authenticated user profile
            user_data = self.api_client.get_authenticated_user()
            if user_data:
                user_entity = self.entity_mapper.map_user(user_data, organisation_id)
                self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, user_entity)
                items_synced += 1

            # 2. Sync user repositories
            repositories = self.api_client.get_user_repositories()
            for repo_data in repositories:
                repo_items = self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)
                items_synced += repo_items

            # 3. Sync user organizations (if any)
            organizations = self.api_client.get_user_organizations()
            for org_data in organizations:
                org_entity = self.entity_mapper.map_organization(org_data, organisation_id)
                self._create_or_update_entity(self.organization_queries.CREATE_OR_UPDATE_GITHUB_ORGANIZATION, org_entity)

                if user_data:
                    # Get detailed membership information for role and join date
                    member_details = self.api_client.get_organization_member_details(org_data['login'], user_entity['login'])
                    role = 'member'  # default
                    join_date = datetime.utcnow().isoformat()  # fallback

                    if member_details:
                        role = member_details.get('role', 'member')  # 'admin' or 'member'
                        join_date = member_details.get('created_at', datetime.utcnow().isoformat())

                    self._create_relationship(self.organization_queries.ADD_MEMBER_TO_ORGANIZATION, {
                        'login': user_entity['login'],
                        'github_org_id': org_entity['id'],  # Fixed: use 'id' instead of 'github_org_id'
                        'role': role,
                        'join_date': join_date,
                        'state': member_details.get('state', 'active') if member_details else 'active',
                        'visibility': member_details.get('visibility', 'private') if member_details else 'private'
                    })
                items_synced += 1

            logger.info(f"Successfully completed GitHub user sync: {items_synced} items")
            return True, f"Successfully synced GitHub user data", items_synced

        except Exception as e:
            logger.error(f"Error in GitHub user sync: {str(e)}")
            return False, f"User sync failed: {str(e)}", 0

    def sync_repository(self, repo_data: Dict, organisation_id: str, full_sync: bool = False) -> int:
        """
        Sync a single repository with all its data.

        Args:
            repo_data: Repository data from GitHub API
            organisation_id: The organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Number of items synced
        """
        return self._sync_repository_comprehensive(repo_data, organisation_id, full_sync)

    def _sync_repository_comprehensive(self, repo_data: Dict, organisation_id: str, full_sync: bool = False) -> int:
        """Comprehensive repository sync following Google Drive patterns."""
        try:
            items_synced = 0
            owner = repo_data.get('owner', {}).get('login')
            repo_name = repo_data.get('name')

            if not owner or not repo_name:
                logger.warning(f"Invalid repository data: missing owner or name")
                return 0

            logger.info(f"Syncing repository: {owner}/{repo_name}")

            # 1. Create repository node
            repo_entity = self.entity_mapper.map_repository(repo_data, organisation_id)
            self._create_or_update_entity(self.repository_queries.CREATE_OR_UPDATE_REPOSITORY, repo_entity)
            items_synced += 1

            # 1.1. Check for fork relationship and get detailed repository info
            detailed_repo = self.api_client.get_repository(owner, repo_name)
            if detailed_repo and detailed_repo.get('fork') and detailed_repo.get('source'):
                source_repo = detailed_repo['source']
                source_owner = source_repo.get('owner', {}).get('login')
                source_name = source_repo.get('name')

                if source_owner and source_name:
                    # Ensure the source repository exists in our graph
                    source_repo_entity = self.entity_mapper.map_repository(source_repo, organisation_id)
                    self._create_or_update_entity(self.repository_queries.CREATE_OR_UPDATE_REPOSITORY, source_repo_entity)

                    # Create FORKS_FROM relationship
                    self._create_relationship(self.relationship_queries.CREATE_FORK_RELATIONSHIP, {
                        'fork_repository_id': repo_entity['id'],
                        'source_repository_id': source_repo_entity['id'],
                        'forked_at': detailed_repo.get('created_at', datetime.utcnow().isoformat())
                    })
                    items_synced += 1

            # 2. Sync repository owner
            if repo_data.get('owner'):
                owner_entity = self.entity_mapper.map_user(repo_data['owner'], organisation_id)
                self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, owner_entity)
                self._create_relationship(self.user_queries.CREATE_USER_REPOSITORY_OWNERSHIP,
                                       {'login': owner_entity['login'], 'repository_id': repo_entity['id'],  # Fixed: use 'id' instead of 'repository_id'
                                        'organisation_id': organisation_id, 'ownership_type': 'owner',
                                        'granted_at': datetime.utcnow().isoformat()})
                items_synced += 1

            # 2.1. Sync repository collaborators (CONTRIBUTES_TO relationships)
            collaborators = self.api_client.get_repository_collaborators(owner, repo_name)
            for collaborator_data in collaborators:
                collaborator_entity = self.entity_mapper.map_user(collaborator_data, organisation_id)
                self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, collaborator_entity)

                # Determine role from permissions
                permissions = collaborator_data.get('permissions', {})
                role = 'read'  # default
                if permissions.get('admin'):
                    role = 'admin'
                elif permissions.get('push'):
                    role = 'write'
                elif permissions.get('pull'):
                    role = 'read'

                # Create CONTRIBUTES_TO relationship with enhanced properties
                self._create_relationship(self.repository_queries.ADD_REPOSITORY_COLLABORATOR, {
                    'repository_id': repo_entity['id'],
                    'login': collaborator_entity['login'],
                    'role': role,
                    'granted_at': datetime.utcnow().isoformat(),
                    'permissions_admin': permissions.get('admin', False),
                    'permissions_push': permissions.get('push', False),
                    'permissions_pull': permissions.get('pull', False),
                    'site_admin': collaborator_data.get('site_admin', False)
                })
                items_synced += 1

            # 2.2. Sync repository contributors (for contribution history)
            contributors = self.api_client.get_repository_contributors(owner, repo_name)
            for contributor_data in contributors:
                contributor_entity = self.entity_mapper.map_user(contributor_data, organisation_id)
                self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, contributor_entity)

                # Update CONTRIBUTES_TO relationship with contribution stats if it exists
                # This will add first_contribution_at and last_contribution_at based on commit history
                self._update_contributor_relationship(repo_entity['id'], contributor_entity['login'], organisation_id)
                items_synced += 1

            # 3. Sync branches
            branches = self.api_client.get_repository_branches(owner, repo_name)
            for branch_data in branches:
                branch_entity = {
                    'name': branch_data.get('name'),
                    'repository_id': repo_entity['id'],  # Fixed: use 'id' instead of 'repository_id'
                    'organisation_id': organisation_id,
                    'last_commit_sha': branch_data.get('commit', {}).get('sha'),
                    'protected': branch_data.get('protected', False),
                    'default': branch_data['name'] == repo_data.get('default_branch'),
                    'created_at': datetime.utcnow().isoformat(),
                    'updated_at': datetime.utcnow().isoformat()
                }
                self._create_or_update_entity(self.repository_queries.CREATE_OR_UPDATE_BRANCH, branch_entity)
                items_synced += 1

            if full_sync:
                # 4. Sync issues
                issues = self.api_client.get_repository_issues(owner, repo_name)
                for issue_data in issues:
                    # Skip pull requests (they appear in issues API)
                    if issue_data.get('pull_request'):
                        continue

                    issue_entity = self.entity_mapper.map_issue(issue_data, organisation_id, repo_entity['id'])  # Fixed: use 'id' instead of 'repository_id'
                    self._create_or_update_entity(self.issue_queries.CREATE_OR_UPDATE_ISSUE, issue_entity)
                    items_synced += 1

                    # Create comprehensive issue relationships in single operation
                    creator_login = None
                    if issue_data.get('user'):
                        creator_entity = self.entity_mapper.map_user(issue_data['user'], organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, creator_entity)
                        creator_login = creator_entity['login']

                    # Extract assignee logins
                    assignee_logins = []
                    for assignee in issue_data.get('assignees', []):
                        assignee_entity = self.entity_mapper.map_user(assignee, organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, assignee_entity)
                        assignee_logins.append(assignee_entity['login'])

                    # Determine closer if issue is closed
                    closer_login = None
                    if issue_data.get('state') == 'closed' and issue_data.get('closed_by'):
                        closer_entity = self.entity_mapper.map_user(issue_data['closed_by'], organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, closer_entity)
                        closer_login = closer_entity['login']

                    # Use comprehensive relationship creation query with fallback
                    try:
                        self._create_relationship(self.issue_queries.CREATE_COMPREHENSIVE_ISSUE_RELATIONSHIPS, {
                            'issue_id': issue_entity['issue_id'],
                            'repository_id': repo_entity['id'],
                            'created_at': issue_data.get('created_at'),
                            'creator_login': creator_login,
                            'assignee_logins': assignee_logins,
                            'state': issue_data.get('state'),
                            'closed_at': issue_data.get('closed_at'),
                            'closer_login': closer_login
                        })
                    except Exception as e:
                        logger.warning(f"Comprehensive issue relationship creation failed, using fallback: {str(e)}")
                        # Fallback to individual relationship creation
                        if creator_login:
                            self._create_relationship(self.issue_queries.CREATE_ISSUE_CREATOR_RELATIONSHIP, {
                                'issue_id': issue_entity['issue_id'],
                                'creator_login': creator_login,
                                'repository_id': repo_entity['id'],
                                'created_at': issue_data.get('created_at')
                            })

                        # Create individual assignee relationships
                        for assignee_login in assignee_logins:
                            self._create_relationship(self.issue_queries.CREATE_ISSUE_ASSIGNEE_RELATIONSHIP, {
                                'issue_id': issue_entity['issue_id'],
                                'assignee_login': assignee_login,
                                'assigned_at': issue_data.get('created_at'),
                                'assigned_by': creator_login or 'unknown'
                            })

                    # Sync issue comments
                    comments = self.api_client.get_issue_comments(owner, repo_name, issue_data['number'])
                    for comment_data in comments:
                        self._create_comment(comment_data, organisation_id, issue_id=issue_entity['issue_id'])
                        items_synced += 1

                # 5. Sync pull requests
                pull_requests = self.api_client.get_repository_pull_requests(owner, repo_name)
                for pr_data in pull_requests:
                    pr_entity = self.entity_mapper.map_pull_request(pr_data, organisation_id, repo_entity['id'])  # Fixed: use 'id' instead of 'repository_id'
                    self._create_or_update_entity(self.pull_request_queries.CREATE_OR_UPDATE_PULL_REQUEST, pr_entity)
                    items_synced += 1

                    # Create comprehensive PR relationships in single operation
                    creator_login = None
                    if pr_data.get('user'):
                        creator_entity = self.entity_mapper.map_user(pr_data['user'], organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, creator_entity)
                        creator_login = creator_entity['login']

                    # Extract assignee and reviewer logins
                    assignee_logins = []
                    for assignee in pr_data.get('assignees', []):
                        assignee_entity = self.entity_mapper.map_user(assignee, organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, assignee_entity)
                        assignee_logins.append(assignee_entity['login'])

                    reviewer_logins = []
                    for reviewer in pr_data.get('requested_reviewers', []):
                        reviewer_entity = self.entity_mapper.map_user(reviewer, organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, reviewer_entity)
                        reviewer_logins.append(reviewer_entity['login'])

                    # Use comprehensive relationship creation query with fallback
                    try:
                        self._create_relationship(self.pull_request_queries.CREATE_COMPREHENSIVE_PR_RELATIONSHIPS, {
                            'pr_id': pr_entity['pr_id'],
                            'repository_id': repo_entity['id'],
                            'created_at': pr_data.get('created_at'),
                            'creator_login': creator_login,
                            'head_ref': pr_data.get('head', {}).get('ref'),
                            'base_ref': pr_data.get('base', {}).get('ref'),
                            'draft': pr_data.get('draft', False),
                            'additions': pr_data.get('additions', 0),
                            'deletions': pr_data.get('deletions', 0),
                            'changed_files': pr_data.get('changed_files', 0),
                            'commits_count': pr_data.get('commits', 0),
                            'mergeable': pr_data.get('mergeable'),
                            'mergeable_state': pr_data.get('mergeable_state'),
                            'assignee_logins': assignee_logins,
                            'reviewer_logins': reviewer_logins
                        })
                    except Exception as e:
                        logger.warning(f"Comprehensive PR relationship creation failed, using fallback: {str(e)}")
                        # Fallback to individual relationship creation
                        if creator_login:
                            self._create_relationship(self.pull_request_queries.CREATE_PR_CREATOR_RELATIONSHIP, {
                                'pr_id': pr_entity['pr_id'],
                                'creator_login': creator_login,
                                'repository_id': repo_entity['id'],
                                'created_at': pr_data.get('created_at'),
                                'head_ref': pr_data.get('head', {}).get('ref'),
                                'base_ref': pr_data.get('base', {}).get('ref'),
                                'draft': pr_data.get('draft', False)
                            })

                        # Create individual assignee relationships
                        for assignee_login in assignee_logins:
                            self._create_relationship(self.issue_queries.CREATE_ISSUE_ASSIGNEE_RELATIONSHIP, {
                                'issue_id': pr_entity['pr_id'],  # Using same relationship for PRs
                                'assignee_login': assignee_login,
                                'assigned_at': pr_data.get('created_at'),
                                'assigned_by': creator_login or 'unknown'
                            })

                    # Create MERGES_BRANCH relationship if PR is merged
                    if pr_data.get('merged') and pr_data.get('merged_by'):
                        merger_entity = self.entity_mapper.map_user(pr_data['merged_by'], organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, merger_entity)

                        head_ref = pr_data.get('head', {}).get('ref')
                        base_ref = pr_data.get('base', {}).get('ref')
                        if head_ref:  # Create relationship for the merged branch
                            self._create_relationship(self.repository_queries.CREATE_MERGES_BRANCH_RELATIONSHIP, {
                                'user_login': merger_entity['login'],
                                'repository_id': repo_entity['id'],
                                'branch_name': head_ref,
                                'merged_at': pr_data.get('merged_at'),
                                'merge_commit_sha': pr_data.get('merge_commit_sha'),
                                'target_branch': base_ref
                            })

                    # Sync PR reviews
                    reviews = self.api_client.get_pr_reviews(owner, repo_name, pr_data['number'])
                    for review_data in reviews:
                        self._create_review(review_data, organisation_id, pr_entity['pr_id'])
                        items_synced += 1

                # 6. Sync commits (with selective detailed sync for recent commits)
                commits = self.api_client.get_repository_commits(owner, repo_name)
                recent_commits_limit = 500  # 80/20 rule: get detailed info for recent commits only

                for i, commit_data in enumerate(commits):
                    commit_entity = self.entity_mapper.map_commit(commit_data, organisation_id, repo_entity['id'])  # Fixed: use 'id' instead of 'repository_id'
                    self._create_or_update_entity(self.commit_queries.CREATE_OR_UPDATE_COMMIT, commit_entity)
                    items_synced += 1

                    # Create comprehensive commit relationships in single operation
                    author_login = None
                    if commit_data.get('author'):
                        author_entity = self.entity_mapper.map_user(commit_data['author'], organisation_id)
                        self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, author_entity)
                        author_login = author_entity['login']

                    commit_info = commit_data.get('commit', {})
                    stats = commit_data.get('stats', {})
                    default_branch = repo_entity.get('default_branch', 'main')
                    parent_shas = [parent.get('sha') for parent in commit_data.get('parents', []) if parent.get('sha')]

                    # Use comprehensive relationship creation query with fallback
                    try:
                        self._create_relationship(self.commit_queries.CREATE_COMPREHENSIVE_COMMIT_RELATIONSHIPS, {
                            'sha': commit_entity['sha'],
                            'repository_id': repo_entity['id'],
                            'committed_at': commit_entity['date'],
                            'author_login': author_login,
                            'authored_at': commit_info.get('author', {}).get('date'),
                            'additions': stats.get('additions', 0),
                            'deletions': stats.get('deletions', 0),
                            'total_changes': stats.get('total', 0),
                            'files_changed': len(commit_data.get('files', [])),
                            'verification_verified': commit_info.get('verification', {}).get('verified', False),
                            'branch_name': default_branch,
                            'is_merge_commit': len(commit_data.get('parents', [])) > 1,
                            'parent_shas': parent_shas
                        })
                    except Exception as e:
                        logger.warning(f"Comprehensive commit relationship creation failed, using fallback: {str(e)}")
                        # Fallback to individual relationship creation
                        if author_login:
                            self._create_relationship(self.commit_queries.CREATE_COMMIT_AUTHOR_RELATIONSHIP, {
                                'sha': commit_entity['sha'],
                                'author_login': author_login,
                                'authored_at': commit_info.get('author', {}).get('date')
                            })

                        self._create_relationship(self.commit_queries.CREATE_COMMIT_REPOSITORY_RELATIONSHIP, {
                            'sha': commit_entity['sha'],
                            'repository_id': repo_entity['id'],
                            'committed_at': commit_entity['date']
                        })

                    # Get detailed commit information for recent commits (80/20 rule)
                    if i < recent_commits_limit:
                        detailed_commit = self.api_client.get_commit_details(owner, repo_name, commit_entity['sha'])
                        if detailed_commit:
                            # Create parent relationships
                            if detailed_commit.get('parents'):
                                for parent in detailed_commit['parents']:
                                    parent_sha = parent.get('sha')
                                    if parent_sha:
                                        # Create PARENT_COMMIT relationship
                                        self._create_relationship(self.commit_queries.CREATE_PARENT_COMMIT_RELATIONSHIP, {
                                            'child_sha': commit_entity['sha'],
                                            'parent_sha': parent_sha,
                                            'repository_id': repo_entity['id'],
                                            'relationship_type': 'parent'
                                        })

                            # Update commit with detailed stats
                            stats = detailed_commit.get('stats', {})
                            if stats:
                                # Update commit entity with stats
                                self._create_or_update_entity(self.commit_queries.UPDATE_COMMIT_STATS, {
                                    'sha': commit_entity['sha'],
                                    'repository_id': repo_entity['id'],
                                    'additions': stats.get('additions', 0),
                                    'deletions': stats.get('deletions', 0),
                                    'total_changes': stats.get('total', 0)
                                })

                            # Create file relationships using batch operations
                            files = detailed_commit.get('files', [])
                            if files:
                                created_files = []
                                deleted_files = []
                                modified_files = []

                                for file_info in files:
                                    file_path = file_info.get('filename')
                                    if file_path:
                                        # Ensure the file exists in our graph (it should from file sync)
                                        file_entity = self.entity_mapper.map_file({
                                            'path': file_path,
                                            'sha': file_info.get('sha'),
                                            'size': 0  # We don't have size from commit API
                                        }, organisation_id, repo_entity['id'], owner, repo_name)

                                        # Create or update the file (in case it wasn't synced in file sync)
                                        self._create_or_update_entity(self.file_queries.CREATE_OR_UPDATE_FILE, file_entity)

                                        # Categorize file changes for batch processing
                                        file_status = file_info.get('status')
                                        file_change = {
                                            'path': file_path,
                                            'lines_added': file_info.get('additions', 0),
                                            'lines_removed': file_info.get('deletions', 0)
                                        }

                                        if file_status == 'added':
                                            created_files.append(file_change)
                                        elif file_status == 'removed':
                                            deleted_files.append(file_change)
                                        else:
                                            modified_files.append(file_change)

                                # Create all file relationships in a single batch operation with fallback
                                try:
                                    self._create_relationship(self.commit_queries.CREATE_BATCH_FILE_RELATIONSHIPS, {
                                        'sha': commit_entity['sha'],
                                        'repository_id': repo_entity['id'],
                                        'committed_at': commit_entity['date'],
                                        'created_files': created_files,
                                        'deleted_files': deleted_files,
                                        'modified_files': modified_files
                                    })
                                except Exception as e:
                                    logger.warning(f"Batch file relationship creation failed, using individual creation: {str(e)}")
                                    # Fallback to individual file relationship creation
                                    for file_change in created_files:
                                        try:
                                            self._create_relationship(self.commit_queries.CREATE_CREATES_FILE_RELATIONSHIP, {
                                                'sha': commit_entity['sha'],
                                                'repository_id': repo_entity['id'],
                                                'file_path': file_change['path'],
                                                'created_at': commit_entity['date'],
                                                'lines_added': file_change['lines_added']
                                            })
                                        except Exception as fe:
                                            logger.error(f"Failed to create CREATES_FILE relationship: {str(fe)}")

                                    for file_change in deleted_files:
                                        try:
                                            self._create_relationship(self.commit_queries.CREATE_DELETES_FILE_RELATIONSHIP, {
                                                'sha': commit_entity['sha'],
                                                'repository_id': repo_entity['id'],
                                                'file_path': file_change['path'],
                                                'deleted_at': commit_entity['date'],
                                                'lines_removed': file_change['lines_removed']
                                            })
                                        except Exception as fe:
                                            logger.error(f"Failed to create DELETES_FILE relationship: {str(fe)}")

                                    for file_change in modified_files:
                                        try:
                                            self._create_relationship(self.relationship_queries.CREATE_COMMIT_MODIFIES_FILE_RELATIONSHIP, {
                                                'commit_sha': commit_entity['sha'],
                                                'file_path': file_change['path'],
                                                'repository_id': repo_entity['id'],
                                                'modification_type': 'modified',
                                                'lines_added': file_change['lines_added'],
                                                'lines_removed': file_change['lines_removed']
                                            })
                                        except Exception as fe:
                                            logger.error(f"Failed to create MODIFIES_FILE relationship: {str(fe)}")

                            # Sync commit comments
                            commit_comments = self.api_client.get_commit_comments(owner, repo_name, commit_entity['sha'])
                            for comment_data in commit_comments:
                                self._create_comment(comment_data, organisation_id, commit_sha=commit_entity['sha'])
                                items_synced += 1
                    else:
                        # For older commits, just create basic parent relationships without detailed API calls
                        parents = commit_data.get('parents', [])
                        for parent in parents:
                            parent_sha = parent.get('sha')
                            if parent_sha:
                                # Create PARENT_COMMIT relationship
                                self._create_relationship(self.commit_queries.CREATE_PARENT_COMMIT_RELATIONSHIP, {
                                    'child_sha': commit_entity['sha'],
                                    'parent_sha': parent_sha,
                                    'repository_id': repo_entity['id'],
                                    'relationship_type': 'parent'
                                })

            # 7. Sync tags and releases
            if full_sync:
                # Sync tags
                tags = self.api_client.get_repository_tags(owner, repo_name)
                for tag_data in tags:
                    tag_entity = self.entity_mapper.map_tag(tag_data, organisation_id, repo_entity['id'])
                    self._create_or_update_entity(self.tag_queries.CREATE_OR_UPDATE_TAG, tag_entity)
                    items_synced += 1

                    # Create tag-repository relationship
                    self._create_relationship(self.tag_queries.CREATE_TAG_REPOSITORY_RELATIONSHIP, {
                        'tag_name': tag_entity['name'],
                        'repository_id': repo_entity['id'],
                        'created_at': tag_entity['created_at']
                    })

                # Sync releases
                releases = self.api_client.get_repository_releases(owner, repo_name)
                for release_data in releases:
                    release_entity = self.entity_mapper.map_release(release_data, organisation_id, repo_entity['id'])
                    self._create_or_update_entity(self.release_queries.CREATE_OR_UPDATE_RELEASE, release_entity)
                    items_synced += 1

                    # Create release-repository relationship
                    self._create_relationship(self.release_queries.CREATE_RELEASE_REPOSITORY_RELATIONSHIP, {
                        'release_id': release_entity['release_id'],
                        'repository_id': repo_entity['id'],
                        'created_at': release_entity['created_at']
                    })

                    # Create TAGS_RELEASE relationship if tag exists
                    tag_name = release_data.get('tag_name')
                    if tag_name:
                        self._create_relationship(self.relationship_queries.CREATE_TAGS_RELEASE_RELATIONSHIP, {
                            'tag_name': tag_name,
                            'release_id': release_entity['release_id'],
                            'repository_id': repo_entity['id'],
                            'tagged_at': release_entity.get('published_at', release_entity['created_at'])
                        })

            # 8. Sync files and directories (using Git Trees API)
            if full_sync:
                files_and_dirs_synced = self._sync_repository_files_and_dirs(owner, repo_name, repo_entity, organisation_id)
                items_synced += files_and_dirs_synced

            # 9. Create organizational relationships
            self._create_organizational_relationships(repo_entity['id'], organisation_id)  # Fixed: use 'id' instead of 'repository_id'

            logger.info(f"Successfully synced repository {owner}/{repo_name}: {items_synced} items")
            return items_synced

        except Exception as e:
            logger.error(f"Error syncing repository {owner}/{repo_name}: {str(e)}")
            return 0

    def _sync_repository_files_and_dirs(self, owner: str, repo_name: str, repo_entity: Dict, organisation_id: str) -> int:
        """
        Sync repository files and directories using the Git Trees API.
        This is much more efficient than recursively calling the contents API.

        Args:
            owner: Repository owner
            repo_name: Repository name
            repo_entity: Repository entity data
            organisation_id: Organisation ID

        Returns:
            Number of files and directories synced
        """
        try:
            items_synced = 0
            repository_id = repo_entity['id']

            # Get the default branch to find the root tree SHA
            default_branch = repo_entity.get('default_branch', 'main')

            # Get the branch to find the tree SHA
            branches = self.api_client.get_repository_branches(owner, repo_name)
            tree_sha = None

            for branch in branches:
                if branch.get('name') == default_branch:
                    tree_sha = branch.get('commit', {}).get('sha')
                    break

            if not tree_sha:
                logger.warning(f"Could not find tree SHA for {owner}/{repo_name} default branch {default_branch}")
                return 0

            # Get the full tree structure recursively
            tree_data = self.api_client.get_repository_tree(owner, repo_name, tree_sha, recursive=True)

            if not tree_data or not tree_data.get('tree'):
                logger.warning(f"No tree data found for {owner}/{repo_name}")
                return 0

            # Process each item in the tree
            directories_created = set()  # Track created directories to avoid duplicates

            for item in tree_data['tree']:
                item_type = item.get('type')
                item_path = item.get('path', '')

                if item_type == 'blob':  # File
                    file_entity = self.entity_mapper.map_file(item, organisation_id, repository_id, owner, repo_name)
                    self._create_or_update_entity(self.file_queries.CREATE_OR_UPDATE_FILE, file_entity)
                    items_synced += 1

                    # Create BELONGS_TO relationship between file and repository
                    self._create_relationship(self.relationship_queries.CREATE_FILE_REPOSITORY_RELATIONSHIP, {
                        'file_path': file_entity['path'],
                        'repository_id': repository_id,
                        'created_at': datetime.utcnow().isoformat()
                    })

                    # Create directory structure for this file's parent directories
                    parent_dirs = self._get_parent_directories(item_path)
                    for parent_dir in parent_dirs:
                        if parent_dir not in directories_created:
                            dir_entity = self.entity_mapper.map_directory({
                                'path': parent_dir,
                                'type': 'tree',
                                'sha': None  # We don't have SHA for intermediate directories
                            }, organisation_id, repository_id, owner, repo_name)

                            self._create_or_update_entity(self.file_queries.CREATE_OR_UPDATE_DIRECTORY, dir_entity)
                            directories_created.add(parent_dir)
                            items_synced += 1

                            # Create BELONGS_TO relationship between directory and repository
                            self._create_relationship(self.relationship_queries.CREATE_DIRECTORY_REPOSITORY_RELATIONSHIP, {
                                'directory_path': dir_entity['path'],
                                'repository_id': repository_id,
                                'created_at': datetime.utcnow().isoformat()
                            })

                    # Create CONTAINS relationships between directories and files
                    immediate_parent = '/'.join(item_path.split('/')[:-1]) if '/' in item_path else ''
                    if immediate_parent and immediate_parent in directories_created:
                        self._create_relationship(self.relationship_queries.CREATE_DIRECTORY_CONTAINS_FILE_RELATIONSHIP, {
                            'directory_path': immediate_parent,
                            'file_path': file_entity['path'],
                            'repository_id': repository_id,
                            'added_at': datetime.utcnow().isoformat()
                        })

                elif item_type == 'tree':  # Directory
                    if item_path not in directories_created:
                        dir_entity = self.entity_mapper.map_directory(item, organisation_id, repository_id, owner, repo_name)
                        self._create_or_update_entity(self.file_queries.CREATE_OR_UPDATE_DIRECTORY, dir_entity)
                        directories_created.add(item_path)
                        items_synced += 1

                        # Create BELONGS_TO relationship between directory and repository
                        self._create_relationship(self.relationship_queries.CREATE_DIRECTORY_REPOSITORY_RELATIONSHIP, {
                            'directory_path': dir_entity['path'],
                            'repository_id': repository_id,
                            'created_at': datetime.utcnow().isoformat()
                        })

            # Create parent-child relationships between directories
            for directory_path in directories_created:
                parent_path = '/'.join(directory_path.split('/')[:-1]) if '/' in directory_path else ''
                if parent_path and parent_path in directories_created:
                    self._create_relationship(self.relationship_queries.CREATE_DIRECTORY_CONTAINS_DIRECTORY_RELATIONSHIP, {
                        'parent_directory_path': parent_path,
                        'child_directory_path': directory_path,
                        'repository_id': repository_id,
                        'added_at': datetime.utcnow().isoformat()
                    })

            logger.info(f"Synced {items_synced} files and directories for {owner}/{repo_name}")
            return items_synced

        except Exception as e:
            logger.error(f"Error syncing files and directories for {owner}/{repo_name}: {str(e)}")
            return 0

    def _get_parent_directories(self, file_path: str) -> List[str]:
        """
        Get all parent directory paths for a given file path.

        Args:
            file_path: Full file path

        Returns:
            List of parent directory paths
        """
        if not file_path or '/' not in file_path:
            return []

        parts = file_path.split('/')[:-1]  # Remove filename
        directories = []

        for i in range(len(parts)):
            dir_path = '/'.join(parts[:i+1])
            directories.append(dir_path)

        return directories

    # Helper methods for creating and updating entities
    def _create_or_update_entity(self, query: str, entity_data: Dict):
        """Generic method to create or update any entity."""
        try:
            execute_write_query(query, entity_data)
            logger.debug(f"Created/updated entity: {entity_data.get('id', entity_data.get('name', 'unknown'))}")
        except Exception as e:
            logger.error(f"Error creating/updating entity: {str(e)}")

    def _create_relationship(self, query: str, relationship_data: Dict):
        """Generic method to create relationships."""
        try:
            relationship_data['created_at'] = datetime.utcnow().isoformat()
            execute_write_query(query, relationship_data)
            logger.debug(f"Created relationship: {relationship_data}")
        except Exception as e:
            logger.error(f"Error creating relationship: {str(e)}")

    def _create_comment(self, comment_data: Dict, organisation_id: str, issue_id: int = None, pull_request_id: int = None, commit_sha: str = None):
        """Create or update GitHub comment."""
        try:
            params = {
                'comment_id': comment_data.get('id'),
                'organisation_id': organisation_id,
                'body': comment_data.get('body'),
                'html_url': comment_data.get('html_url'),
                'created_at': comment_data.get('created_at'),
                'updated_at': comment_data.get('updated_at'),
                'issue_id': issue_id,
                'pull_request_id': pull_request_id
            }

            execute_write_query(self.comment_queries.CREATE_OR_UPDATE_COMMENT, params)
            logger.debug(f"Created/updated GitHub comment: {comment_data.get('id')}")

            # Create comment author relationship and COMMENTS_ON relationships
            if comment_data.get('user'):
                author_entity = self.entity_mapper.map_user(comment_data['user'], organisation_id)
                self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, author_entity)

                # Create comment authorship relationship
                self._create_relationship(self.comment_queries.CREATE_COMMENT_AUTHOR_RELATIONSHIP, {
                    'comment_id': comment_data.get('id'),
                    'author_login': author_entity['login'],
                    'created_at': comment_data.get('created_at')
                })

                # Create COMMENTS_ON relationship based on what the comment is on
                if issue_id:
                    self._create_relationship(self.comment_queries.CREATE_COMMENTS_ON_ISSUE_RELATIONSHIP, {
                        'user_login': author_entity['login'],
                        'issue_id': issue_id,
                        'commented_at': comment_data.get('created_at')
                    })
                elif pull_request_id:
                    self._create_relationship(self.comment_queries.CREATE_COMMENTS_ON_PR_RELATIONSHIP, {
                        'user_login': author_entity['login'],
                        'pr_id': pull_request_id,
                        'commented_at': comment_data.get('created_at')
                    })
                elif commit_sha:
                    self._create_relationship(self.comment_queries.CREATE_COMMENTS_ON_COMMIT_RELATIONSHIP, {
                        'user_login': author_entity['login'],
                        'commit_sha': commit_sha,
                        'commented_at': comment_data.get('created_at')
                    })

        except Exception as e:
            logger.error(f"Error creating/updating GitHub comment: {str(e)}")

    def _create_review(self, review_data: Dict, organisation_id: str, pull_request_id: int):
        """Create or update GitHub review."""
        try:
            params = {
                'review_id': review_data.get('id'),
                'organisation_id': organisation_id,
                'pull_request_id': pull_request_id,
                'state': review_data.get('state'),
                'body': review_data.get('body'),
                'html_url': review_data.get('html_url'),
                'submitted_at': review_data.get('submitted_at')
            }

            execute_write_query(self.review_queries.CREATE_OR_UPDATE_REVIEW, params)
            logger.debug(f"Created/updated GitHub review: {review_data.get('id')}")

            # Create review author relationship
            if review_data.get('user'):
                reviewer_entity = self.entity_mapper.map_user(review_data['user'], organisation_id)
                self._create_or_update_entity(self.user_queries.CREATE_OR_FIND_USER_BY_LOGIN, reviewer_entity)
                self._create_relationship(self.review_queries.CREATE_REVIEW_AUTHOR_RELATIONSHIP, {
                    'review_id': review_data.get('id'),
                    'author_login': reviewer_entity['login'],
                    'submitted_at': review_data.get('submitted_at')
                })

        except Exception as e:
            logger.error(f"Error creating/updating GitHub review: {str(e)}")

    def _update_contributor_relationship(self, repository_id: str, contributor_login: str, organisation_id: str) -> None:
        """Update CONTRIBUTES_TO relationship with contribution history."""
        try:
            # Query for the user's first and last commits in this repository
            query = f"""
            MATCH (user:{github_schema.get_node_labels()[1]} {{login: $contributor_login}})
            MATCH (repo:{github_schema.get_node_labels()[0]} {{id: $repository_id}})
            MATCH (user)-[:{github_schema.get_relationship_types()[14]}]->(commit:{github_schema.get_node_labels()[6]})-[:{github_schema.get_relationship_types()[31]}]->(repo)
            WITH user, repo, commit
            ORDER BY commit.created_at
            WITH user, repo, collect(commit) as commits
            WHERE size(commits) > 0
            WITH user, repo, commits[0] as first_commit, commits[-1] as last_commit
            MATCH (user)-[r:{github_schema.get_relationship_types()[1]}]->(repo)
            SET r.first_contribution_at = first_commit.created_at,
                r.last_contribution_at = last_commit.created_at
            RETURN r
            """

            execute_write_query(query, {
                'contributor_login': contributor_login,
                'repository_id': repository_id
            })

        except Exception as e:
            logger.error(f"Error updating contributor relationship: {str(e)}")

    def _create_organizational_relationships(self, repository_id: int, organisation_id: str):
        """Create organizational relationships following Google Drive patterns."""
        try:
            # Create organization-repository relationships (mirroring GDrive's organization access)
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'granted_at': datetime.utcnow().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_ORGANISATION_REPOSITORY_RELATIONSHIP, params)

            # Create department-repository relationships for all departments (mirroring GDrive's department access)
            execute_write_query(self.relationship_queries.CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP, params)

            logger.debug(f"Created organizational relationships for repository: {repository_id}")
        except Exception as e:
            logger.error(f"Error creating organizational relationships: {str(e)}")


class GitHubService:
    """
    Refactored GitHub service using modular components.
    Provides clean integration with the existing connector framework
    while using the new GitHubAPIClient, GitHubEntityMapper, and GitHubSyncService.
    """

    def __init__(self, organisation_id: str, source_id: str, pinecone_service: PineconeService = None, redis_service: RedisService = None):
        self.redis_service = RedisService()
        self.pinecone_service = PineconeService()

        # Initialize modular components
        self.api_client = None  # Will be initialized when credentials are available
        self.entity_mapper = GitHubEntityMapper()
        self.sync_service = None  # Will be initialized when API client is ready

        # Initialize query instances for backward compatibility
        self.user_queries = GitHubUserQueries()
        self.organization_queries = GitHubOrganizationQueries()
        self.repository_queries = GitHubRepositoryQueries()
        self.issue_queries = GitHubIssueQueries()
        self.pull_request_queries = GitHubPullRequestQueries()
        self.commit_queries = GitHubCommitQueries()
        self.file_queries = GitHubFileQueries()
        self.team_queries = GitHubTeamQueries()
        self.tag_queries = GitHubTagQueries()
        self.release_queries = GitHubReleaseQueries()
        self.comment_queries = GitHubCommentQueries()
        self.review_queries = GitHubReviewQueries()
        self.relationship_queries = GitHubRelationshipQueries()
        self.sync_queries = GitHubSyncQueries()

        # Initialize search service
        self.search_service = GitHubSearchService(
            pinecone_service=pinecone_service,
            redis_service=redis_service
        )

    def _initialize_api_client(self, organisation_id: str) -> bool:
        """Initialize API client with credentials."""
        try:
            token = get_source_credentials(
                organisation_id, "github", credential_type="service_account"
            )

            if not token:
                logger.error(f"No GitHub credentials found for organisation {organisation_id}")
                return False

            self.api_client = GitHubAPIClient(token)
            self.sync_service = GitHubSyncService(self.api_client, self.entity_mapper)
            return True

        except Exception as e:
            logger.error(f"Error initializing GitHub API client: {str(e)}")
            return False

    def _schedule_sync(self, user_id: str, organisation_id: str = None, full_sync: bool = False, delay_seconds: int = 0) -> str:
        """
        Schedule a GitHub sync job following Google Drive patterns.

        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organisation (required)
            full_sync: Whether to perform a full sync
            delay_seconds: Delay before executing the job

        Returns:
            The job ID

        Raises:
            ValueError: If organisation_id is None
        """
        # organisation_id is required for sync jobs
        if organisation_id is None:
            raise ValueError("organisation_id is required for scheduling sync jobs")

        job_data = {
            'user_id': user_id,
            'organisation_id': organisation_id,
            'full_sync': full_sync,
            'scheduled_at': (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat()
        }

        # Store in Redis with appropriate expiration
        job_id = f"github_sync:{user_id}:{int(time.time())}"
        self.redis_service.set(
            job_id,
            json.dumps(job_data),
            ex=86400  # 24 hour expiration
        )

        # Add to sorted set for processing
        score = time.time() + delay_seconds
        self.redis_service.zadd("github_sync_queue", {job_id: score})

        return job_id

    def _cancel_scheduled_syncs(self, user_id: str) -> None:
        """
        Cancel all scheduled syncs for a user following Google Drive patterns.

        Args:
            user_id: The ID of the user
        """
        # Find all sync jobs for this user
        pattern = f"github_sync:{user_id}:*"
        keys = self.redis_service.keys(pattern)

        # Remove from sorted set and delete keys
        for key in keys:
            self.redis_service.zrem("github_sync_queue", key)
            self.redis_service.delete(key)

    def _cancel_scheduled_syncs_for_organization(self, organisation_id: str) -> None:
        """
        Cancel all scheduled syncs for an organization following Google Drive patterns.

        Args:
            organisation_id: The ID of the organization
        """
        # Find all GitHub sync jobs
        pattern = f"github_sync:*"
        keys = self.redis_service.keys(pattern)

        cancelled_count = 0
        # Check each job to see if it belongs to this organization
        for key in keys:
            try:
                job_data_str = self.redis_service.get(key)
                if job_data_str:
                    job_data = json.loads(job_data_str)
                    if job_data.get('organisation_id') == organisation_id:
                        self.redis_service.zrem("github_sync_queue", key)
                        self.redis_service.delete(key)
                        cancelled_count += 1
            except Exception as e:
                logger.warning(f"Error checking job {key}: {str(e)}")

        logger.info(f"Cancelled {cancelled_count} scheduled GitHub sync jobs for organization {organisation_id}")

    def sync_github(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int, int]:
        """
        Perform a full sync of GitHub data for an organisation using modular components.

        Args:
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, repositories_synced, total_items_synced)
        """
        try:
            # Validate organisation_id is provided
            if not organisation_id:
                logger.error("organisation_id is required for GitHub sync")
                return False, "organisation_id is required", 0, 0

            logger.info(f"Starting GitHub sync for organisation: {organisation_id}")

            # Note: GitHub Source node is already created in organization service when adding GitHub source

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0, 0

            # Use the new sync service for organization-level sync
            success, message, items_synced = self.sync_service.sync_organization(organisation_id, full_sync)

            if success:
                # Update last sync time
                self._update_last_sync_time(organisation_id)

                # For backward compatibility, estimate repositories synced
                repositories_synced = max(1, items_synced // 10)  # Rough estimate

                logger.info(f"GitHub sync completed: {repositories_synced} repositories, {items_synced} total items")
                return True, f"Successfully synced {repositories_synced} repositories", repositories_synced, items_synced
            else:
                return False, message, 0, 0

        except Exception as e:
            logger.error(f"Error in GitHub sync: {str(e)}")
            return False, f"Sync failed: {str(e)}", 0, 0

    def sync_github_user(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Perform user-level GitHub sync using modular components.

        Args:
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            # Validate organisation_id is provided
            if not organisation_id:
                logger.error("organisation_id is required for GitHub user sync")
                return False, "organisation_id is required", 0

            logger.info(f"Starting GitHub user sync for organisation: {organisation_id}")

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0

            # Use the new sync service for user-level sync
            success, message, items_synced = self.sync_service.sync_user(organisation_id, full_sync)

            if success:
                # Update last sync time
                self._update_last_sync_time(organisation_id)

                logger.info(f"GitHub user sync completed: {items_synced} items")
                return True, message, items_synced
            else:
                return False, message, 0

        except Exception as e:
            logger.error(f"Error in GitHub user sync: {str(e)}")
            return False, f"User sync failed: {str(e)}", 0

    def sync_repository_by_url(self, github_url: str, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync a specific GitHub repository by URL using modular components.

        Args:
            github_url: GitHub repository URL
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync

        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            # Extract repository info from URL
            repo_info = self.extract_repo_info_from_url(github_url)
            if not repo_info:
                return False, f"Invalid GitHub URL: {github_url}", 0

            owner = repo_info['owner']
            repo_name = repo_info['repo']

            logger.info(f"Syncing GitHub repository by URL: {owner}/{repo_name}")

            # Initialize API client with credentials
            if not self._initialize_api_client(organisation_id):
                return False, "Failed to initialize GitHub API client", 0

            # Get repository data using the new API client
            repo_data = self.api_client.get_repository(owner, repo_name)
            if not repo_data:
                return False, f"Failed to fetch repository data for {owner}/{repo_name}", 0

            # Use the new sync service to sync the repository
            items_synced = self.sync_service.sync_repository(repo_data, organisation_id, full_sync)

            if items_synced > 0:
                logger.info(f"Successfully synced GitHub repository: {owner}/{repo_name}, items: {items_synced}")
                return True, f"Successfully synced repository {owner}/{repo_name}", items_synced
            else:
                return False, f"Failed to sync repository {owner}/{repo_name}", 0

        except Exception as e:
            logger.error(f"Error syncing repository by URL: {str(e)}")
            return False, f"Repository sync failed: {str(e)}", 0

    def _update_last_sync_time(self, organisation_id: str):
        """Update last sync time following Google Drive patterns."""
        try:
            source_queries = SourceQueries()
            params = {
                'organisation_id': organisation_id,
                'source_type': SourceType.GITHUB.value,
                'last_sync_at': datetime.utcnow().isoformat()
            }
            execute_write_query(source_queries.UPDATE_SOURCE_LAST_SYNC, params)
            logger.debug(f"Updated last sync time for organisation {organisation_id}")
        except Exception as e:
            logger.error(f"Error updating last sync time: {str(e)}")

    def search_github_content(self, query: str, organisation_id: str, user_id: str = None,
                             search_type: str = "all", limit: int = 10, 
                             filters: Dict[str, Any] = None) -> List[Dict]:
        """
        Enhanced GitHub content search with multiple strategies and entity types.
        
        Args:
            query: Search query string
            organisation_id: Organisation ID
            user_id: Optional user ID for user-specific searches
            search_type: Type of entity to search (repository, pull_request, commit, etc.)
            limit: Maximum number of results
            filters: Additional search filters
            
        Returns:
            List of search results
        """
        try:
            # Validate parameters
            if not organisation_id:
                logger.error("organisation_id is required for searching GitHub content")
                return []

            if not query or not query.strip():
                logger.error("query is required for searching GitHub content")
                return []

            if limit <= 0:
                limit = 10

            # Convert string search type to enum
            try:
                github_search_type = GitHubSearchType(search_type.lower())
            except ValueError:
                github_search_type = GitHubSearchType.ALL

            # Execute search using the search service
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                search_result = loop.run_until_complete(
                    self.search_service.search(
                        query=query,
                        organisation_id=organisation_id,
                        user_id=user_id,
                        search_type=github_search_type,
                        strategy=SearchStrategy.HYBRID,
                        limit=limit,
                        filters=filters
                    )
                )
            finally:
                loop.close()

            if search_result.get('success', False):
                return search_result.get('results', [])
            else:
                logger.error(f"Search failed: {search_result.get('error', 'Unknown error')}")
                return []

        except Exception as e:
            logger.error(f"Error searching GitHub content: {str(e)}")
            return []

    async def advanced_search(self, query: str, organisation_id: str, user_id: str = None,
                             search_type: GitHubSearchType = GitHubSearchType.ALL,
                             strategy: SearchStrategy = SearchStrategy.HYBRID,
                             limit: int = 20, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Advanced search method with full control over search parameters.
        
        Args:
            query: Search query string
            organisation_id: Organisation ID
            user_id: Optional user ID for user-specific searches
            search_type: Type of GitHub entity to search
            strategy: Search strategy to use
            limit: Maximum number of results
            filters: Additional search filters
            
        Returns:
            Complete search results with metadata
        """
        return await self.search_service.search(
            query=query,
            organisation_id=organisation_id,
            user_id=user_id,
            search_type=search_type,
            strategy=strategy,
            limit=limit,
            filters=filters
        )

    def get_sync_statistics(self, organisation_id: str) -> Dict[str, Any]:
        """
        Get sync statistics for an organisation following Google Drive patterns.

        Args:
            organisation_id: Organisation ID

        Returns:
            Dictionary containing sync statistics
        """
        try:
            # Validate required parameters
            if not organisation_id:
                logger.error("organisation_id is required for getting sync statistics")
                return {}

            # Get statistics from various GitHub entities
            stats = {
                'organisation_id': organisation_id,
                'last_updated': datetime.utcnow().isoformat(),
                'entities': {}
            }

            # Use the existing GET_SYNC_STATISTICS query instead of individual count queries
            try:
                result = execute_read_query(
                    self.sync_queries.GET_SYNC_STATISTICS,
                    {'organisation_id': organisation_id}
                )
                if result and result[0]:
                    sync_data = result[0]
                    stats['entities'] = {
                        'repositories': sync_data.get('repositories_count', 0),
                        'files': sync_data.get('files_count', 0),
                        'issues': sync_data.get('issues_count', 0),
                        'pull_requests': sync_data.get('pull_requests_count', 0),
                        'commits': sync_data.get('commits_count', 0),
                        'users': sync_data.get('users_count', 0)
                    }
                else:
                    stats['entities'] = {
                        'repositories': 0,
                        'files': 0,
                        'issues': 0,
                        'pull_requests': 0,
                        'commits': 0,
                        'users': 0
                    }
            except Exception as e:
                logger.warning(f"Error getting sync statistics: {str(e)}")
                stats['entities'] = {
                    'repositories': 0,
                    'files': 0,
                    'issues': 0,
                    'pull_requests': 0,
                    'commits': 0,
                    'users': 0
                }

            # Get last sync time from source
            try:
                source_queries = SourceQueries()
                source_result = execute_read_query(
                    source_queries.GET_SOURCE_BY_TYPE_AND_ORGANISATION,
                    {'organisation_id': organisation_id, 'source_type': SourceType.GITHUB.value}
                )
                if source_result:
                    stats['last_sync_time'] = source_result[0].get('last_sync_at')
                else:
                    stats['last_sync_time'] = None
            except Exception as e:
                logger.warning(f"Error getting last sync time: {str(e)}")
                stats['last_sync_time'] = None

            return stats

        except Exception as e:
            logger.error(f"Error getting GitHub sync statistics: {str(e)}")
            return {
                'organisation_id': organisation_id,
                'error': str(e),
                'last_updated': datetime.utcnow().isoformat()
            }

    def extract_repo_info_from_url(self, github_url: str) -> Optional[Dict[str, str]]:
        """
        Extract repository owner and name from a GitHub URL.

        Args:
            github_url: The GitHub URL

        Returns:
            Dictionary with owner and repo name or None if extraction failed
        """
        try:
            # Handle different URL formats
            # Format 1: https://github.com/owner/repo
            # Format 2: https://github.com/owner/repo/issues/123
            # Format 3: https://github.com/owner/repo/pull/456

            if "github.com/" in github_url:
                # Extract owner/repo from URL
                parts = github_url.split("github.com/")[1].split("/")
                if len(parts) >= 2:
                    owner = parts[0]
                    repo = parts[1]
                    return {"owner": owner, "repo": repo}

            logger.error(f"Unsupported GitHub URL format: {github_url}")
            return None

        except Exception as e:
            logger.error(f"Error extracting repo info from URL: {str(e)}")
            return None
