{"total_tests": 3, "passed_tests": 0, "failed_tests": 3, "test_details": [{"name": "DIRECT PR Search - By User and Repository", "passed": false, "execution_time": 0.7078080177307129, "results_count": 0, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 707.5930000000001, "sample_results": [], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "PRs by test-developer for awesome-python-project (using filters)", "passed": false, "execution_time": 0.5720560550689697, "total_results": 8, "valid_results": 0, "expected_min_results": 1, "expected_author": "test-developer", "expected_repo": "awesome-python-project", "success": true, "sample_valid_results": [], "error": null}, {"name": "PRs by code-reviewer for react-dashboard (using filters)", "passed": false, "execution_time": 0.3195772171020508, "total_results": 8, "valid_results": 0, "expected_min_results": 1, "expected_author": "code-reviewer", "expected_repo": "react-dashboard", "success": true, "sample_valid_results": [], "error": null}]}