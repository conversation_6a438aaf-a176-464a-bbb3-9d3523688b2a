{"total_tests": 3, "passed_tests": 2, "failed_tests": 1, "test_details": [{"name": "DIRECT PR Search - By User and Repository", "passed": false, "execution_time": 0.5818119049072266, "results_count": 0, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 581.598, "sample_results": [], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "PRs by test-developer for awesome-python-project (using filters)", "passed": true, "execution_time": 0.6699981689453125, "total_results": 1, "valid_results": 1, "expected_min_results": 1, "expected_author": "test-developer", "expected_repo": "awesome-python-project", "success": true, "sample_valid_results": [{"id": "github_pr_test_org_8840f2d8_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3338' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_8840f2d8', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 26, 406000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 26, 406000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_8840f2d8_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_8840f2d8', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 25, 725000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 25, 725000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_b11f921c', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_8840f2d8', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 26, 2000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 26, 2000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_8840f2d8_1', 'forks_count': 25}>"}}], "error": null}, {"name": "PRs by code-reviewer for react-dashboard (using filters)", "passed": true, "execution_time": 0.0951840877532959, "total_results": 1, "valid_results": 1, "expected_min_results": 1, "expected_author": "code-reviewer", "expected_repo": "react-dashboard", "success": true, "sample_valid_results": [{"id": "github_pr_test_org_8840f2d8_2", "type": "pull_request", "title": "Fix TypeScript compilation issues", "description": "Resolves compilation errors in the React dashboard components", "url": "https://github.com/test-org/repo/pull/43", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3339' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_8840f2d8', 'number': 43, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 26, 588000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/43', 'created_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 26, 588000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_8840f2d8_2', 'state': 'closed', 'title': 'Fix TypeScript compilation issues', 'body': 'Resolves compilation errors in the React dashboard components'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_8840f2d8', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 25, 903000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 25, 903000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_b11f921c_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3336' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_8840f2d8', 'private': False, 'full_name': 'test-org/react-dashboard', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 26, 120000000, tzinfo=<UTC>), 'stars_count': 89, 'html_url': 'https://github.com/test-org/react-dashboard', 'name': 'react-dashboard', 'created_at': neo4j.time.DateTime(2025, 7, 23, 8, 28, 26, 120000000, tzinfo=<UTC>), 'description': 'Modern React dashboard with TypeScript and Material-UI', 'language': 'TypeScript', 'id': 'github_repo_test_org_8840f2d8_2', 'forks_count': 12}>"}}], "error": null}]}