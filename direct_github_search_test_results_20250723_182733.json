{"total_tests": 28, "passed_tests": 28, "failed_tests": 0, "test_details": [{"name": "DIRECT Repository Search - Exact Name", "passed": true, "execution_time": 0.6011788845062256, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 600.607, "sample_results": [{"id": "github_repo_test_org_ee1a5ca4_1", "type": "repository", "title": "awesome-python-project", "description": "An awesome Python project with machine learning capabilities", "url": "https://github.com/test-org/awesome-python-project", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>", "related_entities": [], "owner": null, "author": null, "repository": null}}], "cypher_query": "\n            MATCH (n:GitHubRepository)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.name) CONTAINS $query OR toLower(n.description) CONTAINS $query \n             ...", "error": null}, {"name": "DIRECT Repository Search - Description", "passed": true, "execution_time": 0.08734583854675293, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 87.052, "sample_results": [{"id": "github_repo_test_org_ee1a5ca4_1", "type": "repository", "title": "awesome-python-project", "description": "An awesome Python project with machine learning capabilities", "url": "https://github.com/test-org/awesome-python-project", "score": 0.7, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>", "related_entities": [], "owner": null, "author": null, "repository": null}}], "cypher_query": "\n            MATCH (n:GitHubRepository)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.name) CONTAINS $query OR toLower(n.description) CONTAINS $query \n             ...", "error": null}, {"name": "DIRECT User Search - Login", "passed": true, "execution_time": 0.35870814323425293, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 358.19, "sample_results": [{"id": "github_user_test_user_8a3adb3b", "type": "user", "title": "Test Developer", "description": "", "url": "https://github.com/test-developer", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "related_entities": [], "owner": null, "author": null, "repository": null}}], "cypher_query": "\n            MATCH (n:GitHubUser)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.login) CONTAINS $query OR toLower(n.name) CONTAINS $query \n                 OR toLow...", "error": null}, {"name": "DIRECT Pull Request Search - Title", "passed": true, "execution_time": 0.502814769744873, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 502.23799999999994, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3341' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "DIRECT Commit Search - Message", "passed": true, "execution_time": 0.43804073333740234, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 437.581, "sample_results": [{"id": "github_commit_test_org_ee1a5ca4_1", "type": "commit", "title": null, "description": "", "url": null, "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3337' labels=frozenset({'GitHubCommit'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'committed_date': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'id': 'github_commit_test_org_ee1a5ca4_1', 'message': 'Add machine learning model for data analysis', 'sha': 'abc123b133bfeab0da4acf'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubCommit)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.message) CONTAINS $query OR toLower(n.sha) CONTAINS $query)\n            \n         ...", "error": null}, {"name": "DIRECT Generic Search - All Types", "passed": true, "execution_time": 0.30249595642089844, "results_count": 2, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 301.937, "sample_results": [{"id": "github_repo_test_org_ee1a5ca4_1", "type": "generic", "title": "awesome-python-project", "description": "An awesome Python project with machine learning capabilities", "url": "https://github.com/test-org/awesome-python-project", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>", "related_entities": ["<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3337' labels=frozenset({'GitHubCommit'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'committed_date': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'id': 'github_commit_test_org_ee1a5ca4_1', 'message': 'Add machine learning model for data analysis', 'sha': 'abc123b133bfeab0da4acf'}>", "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3338' labels=frozenset({'GitHubBranch'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 511000000, tzinfo=<UTC>), 'name': 'main', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 511000000, tzinfo=<UTC>), 'id': 'github_branch_test_org_ee1a5ca4_1'}>", "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3339' labels=frozenset({'GitHubBranch'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 704000000, tzinfo=<UTC>), 'name': 'feature/search-enhancement', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 704000000, tzinfo=<UTC>), 'id': 'github_branch_test_org_ee1a5ca4_2'}>"], "owner": null, "author": null, "repository": null}}], "cypher_query": "\n            MATCH (n)\n            WHERE n.organisation_id = $organisation_id\n            AND any(label in labels(n) WHERE label STARTS WITH 'GitHub')\n            AND (\n                (n.name IS NOT ...", "error": null}, {"name": "COMPLEX PR Search - Author Filter Only", "passed": true, "execution_time": 0.3459041118621826, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 345.584, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3341' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - Repository Filter Only", "passed": true, "execution_time": 0.2934281826019287, "results_count": 2, "expected_min_results": 2, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 292.71799999999996, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_3", "type": "pull_request", "title": "Add unit tests for Python modules", "description": "Comprehensive test coverage for core Python functionality", "url": "https://github.com/test-org/repo/pull/44", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3343' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 44, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/44', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_3', 'state': 'open', 'title': 'Add unit tests for Python modules', 'body': 'Comprehensive test coverage for core Python functionality'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_8a3adb3b_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - State Filter (Open)", "passed": true, "execution_time": 0.4050898551940918, "results_count": 2, "expected_min_results": 2, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 404.587, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_3", "type": "pull_request", "title": "Add unit tests for Python modules", "description": "Comprehensive test coverage for core Python functionality", "url": "https://github.com/test-org/repo/pull/44", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3343' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 44, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/44', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_3', 'state': 'open', 'title': 'Add unit tests for Python modules', 'body': 'Comprehensive test coverage for core Python functionality'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_8a3adb3b_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - State Filter (Closed)", "passed": true, "execution_time": 0.08837676048278809, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 88.056, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_2", "type": "pull_request", "title": "Fix TypeScript compilation issues", "description": "Resolves compilation errors in the React dashboard components", "url": "https://github.com/test-org/repo/pull/43", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3342' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 43, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 158000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/43', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 158000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_2', 'state': 'closed', 'title': 'Fix TypeScript compilation issues', 'body': 'Resolves compilation errors in the React dashboard components'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_8a3adb3b_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3336' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/react-dashboard', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'stars_count': 89, 'html_url': 'https://github.com/test-org/react-dashboard', 'name': 'react-dashboard', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'description': 'Modern React dashboard with TypeScript and Material-UI', 'language': 'TypeScript', 'id': 'github_repo_test_org_ee1a5ca4_2', 'forks_count': 12}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - Combined Author + Repository", "passed": true, "execution_time": 0.2728879451751709, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 272.623, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3341' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - Combined Author + State", "passed": true, "execution_time": 0.2947251796722412, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 294.498, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_2", "type": "pull_request", "title": "Fix TypeScript compilation issues", "description": "Resolves compilation errors in the React dashboard components", "url": "https://github.com/test-org/repo/pull/43", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3342' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 43, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 158000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/43', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 158000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_2', 'state': 'closed', 'title': 'Fix TypeScript compilation issues', 'body': 'Resolves compilation errors in the React dashboard components'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_8a3adb3b_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3336' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/react-dashboard', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'stars_count': 89, 'html_url': 'https://github.com/test-org/react-dashboard', 'name': 'react-dashboard', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'description': 'Modern React dashboard with TypeScript and Material-UI', 'language': 'TypeScript', 'id': 'github_repo_test_org_ee1a5ca4_2', 'forks_count': 12}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - Combined Repository + State", "passed": true, "execution_time": 0.409527063369751, "results_count": 2, "expected_min_results": 2, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 409.096, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_3", "type": "pull_request", "title": "Add unit tests for Python modules", "description": "Comprehensive test coverage for core Python functionality", "url": "https://github.com/test-org/repo/pull/44", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3343' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 44, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/44', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_3', 'state': 'open', 'title': 'Add unit tests for Python modules', 'body': 'Comprehensive test coverage for core Python functionality'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_8a3adb3b_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - Triple Filter (Author + Repository + State)", "passed": true, "execution_time": 0.35546016693115234, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 355.265, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3341' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - Query + Author Filter", "passed": true, "execution_time": 0.2530088424682617, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 252.02799999999996, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3341' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - Query + Repository Filter", "passed": true, "execution_time": 0.2856159210205078, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 285.451, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_3", "type": "pull_request", "title": "Add unit tests for Python modules", "description": "Comprehensive test coverage for core Python functionality", "url": "https://github.com/test-org/repo/pull/44", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3343' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 44, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/44', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_3', 'state': 'open', 'title': 'Add unit tests for Python modules', 'body': 'Comprehensive test coverage for core Python functionality'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_8a3adb3b_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "COMPLEX PR Search - Query + Multiple Filters", "passed": true, "execution_time": 0.32621121406555176, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 325.867, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3341' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "EDGE CASE - Non-existent Author Filter", "passed": true, "execution_time": 0.2455439567565918, "results_count": 0, "expected_min_results": 0, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 245.439, "sample_results": [], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "EDGE CASE - Non-existent Repository Filter", "passed": true, "execution_time": 0.23758983612060547, "results_count": 0, "expected_min_results": 0, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 237.484, "sample_results": [], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "EDGE CASE - Cross-validation (Wrong Author-Repo Combination)", "passed": true, "execution_time": 0.09405112266540527, "results_count": 0, "expected_min_results": 0, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 93.96400000000001, "sample_results": [], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "DIRECT Branch Search - Exact Name", "passed": true, "execution_time": 0.3045520782470703, "results_count": 2, "expected_min_results": 2, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 304.36800000000005, "sample_results": [{"id": "github_branch_test_org_ee1a5ca4_3", "type": "branch", "title": "main", "description": "", "url": null, "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3340' labels=frozenset({'GitHubBranch'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 900000000, tzinfo=<UTC>), 'name': 'main', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 900000000, tzinfo=<UTC>), 'id': 'github_branch_test_org_ee1a5ca4_3'}>", "related_entities": [], "owner": null, "author": null, "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3336' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/react-dashboard', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'stars_count': 89, 'html_url': 'https://github.com/test-org/react-dashboard', 'name': 'react-dashboard', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'description': 'Modern React dashboard with TypeScript and Material-UI', 'language': 'TypeScript', 'id': 'github_repo_test_org_ee1a5ca4_2', 'forks_count': 12}>"}}], "cypher_query": "\n            MATCH (n:GitHubBranch)\n            WHERE n.organisation_id = $organisation_id\n            AND toLower(n.name) CONTAINS $query\n            OPTIONAL MATCH (n)-[:BELONGS_TO]->(repo:GitHubRep...", "error": null}, {"name": "DIRECT Branch Search - Feature Branch", "passed": true, "execution_time": 0.08837103843688965, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 88.2, "sample_results": [{"id": "github_branch_test_org_ee1a5ca4_2", "type": "branch", "title": "feature/search-enhancement", "description": "", "url": null, "score": 0.9, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3339' labels=frozenset({'GitHubBranch'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 704000000, tzinfo=<UTC>), 'name': 'feature/search-enhancement', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 704000000, tzinfo=<UTC>), 'id': 'github_branch_test_org_ee1a5ca4_2'}>", "related_entities": [], "owner": null, "author": null, "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubBranch)\n            WHERE n.organisation_id = $organisation_id\n            AND toLower(n.name) CONTAINS $query\n            OPTIONAL MATCH (n)-[:BELONGS_TO]->(repo:GitHubRep...", "error": null}, {"name": "COMPLEX Multi-Entity Search - Python Related", "passed": true, "execution_time": 0.262725830078125, "results_count": 2, "expected_min_results": 2, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 262.15, "sample_results": [{"id": "github_repo_test_org_ee1a5ca4_1", "type": "generic", "title": "awesome-python-project", "description": "An awesome Python project with machine learning capabilities", "url": "https://github.com/test-org/awesome-python-project", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>", "related_entities": ["<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3337' labels=frozenset({'GitHubCommit'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'committed_date': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 282000000, tzinfo=<UTC>), 'id': 'github_commit_test_org_ee1a5ca4_1', 'message': 'Add machine learning model for data analysis', 'sha': 'abc123b133bfeab0da4acf'}>", "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3338' labels=frozenset({'GitHubBranch'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 511000000, tzinfo=<UTC>), 'name': 'main', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 511000000, tzinfo=<UTC>), 'id': 'github_branch_test_org_ee1a5ca4_1'}>", "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3339' labels=frozenset({'GitHubBranch'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 704000000, tzinfo=<UTC>), 'name': 'feature/search-enhancement', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 704000000, tzinfo=<UTC>), 'id': 'github_branch_test_org_ee1a5ca4_2'}>"], "owner": null, "author": null, "repository": null}}], "cypher_query": "\n            MATCH (n)\n            WHERE n.organisation_id = $organisation_id\n            AND any(label in labels(n) WHERE label STARTS WITH 'GitHub')\n            AND (\n                (n.name IS NOT ...", "error": null}, {"name": "COMPLEX Repository Search - Language Filter Simulation", "passed": true, "execution_time": 0.18123412132263184, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 181.09, "sample_results": [{"id": "github_repo_test_org_ee1a5ca4_2", "type": "repository", "title": "react-dashboard", "description": "Modern React dashboard with TypeScript and Material-UI", "url": "https://github.com/test-org/react-dashboard", "score": 0.7, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3336' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/react-dashboard', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'stars_count': 89, 'html_url': 'https://github.com/test-org/react-dashboard', 'name': 'react-dashboard', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'description': 'Modern React dashboard with TypeScript and Material-UI', 'language': 'TypeScript', 'id': 'github_repo_test_org_ee1a5ca4_2', 'forks_count': 12}>", "related_entities": [], "owner": null, "author": null, "repository": null}}], "cypher_query": "\n            MATCH (n:GitHubRepository)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.name) CONTAINS $query OR toLower(n.description) CONTAINS $query \n             ...", "error": null}, {"name": "PERFORMANCE Test - Empty Query with Filters", "passed": true, "execution_time": 0.2511930465698242, "results_count": 2, "expected_min_results": 2, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 250.938, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_3", "type": "pull_request", "title": "Add unit tests for Python modules", "description": "Comprehensive test coverage for core Python functionality", "url": "https://github.com/test-org/repo/pull/44", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3343' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 44, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/44', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 246000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_3', 'state': 'open', 'title': 'Add unit tests for Python modules', 'body': 'Comprehensive test coverage for core Python functionality'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_8a3adb3b_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "PERFORMANCE Test - Complex Query String", "passed": true, "execution_time": 0.22292089462280273, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "success": true, "service_execution_time_ms": 222.721, "sample_results": [{"id": "github_pr_test_org_ee1a5ca4_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 0.8, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3341' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "cypher_query": "\n            MATCH (n:GitHubPullRequest)\n            WHERE n.organisation_id = $organisation_id\n            AND (toLower(n.title) CONTAINS $query OR toLower(n.body) CONTAINS $query)\n            \n     ...", "error": null}, {"name": "PRs by test-developer for awesome-python-project (using filters)", "passed": true, "execution_time": 0.10366392135620117, "total_results": 1, "valid_results": 1, "expected_min_results": 1, "expected_author": "test-developer", "expected_repo": "awesome-python-project", "success": true, "sample_valid_results": [{"id": "github_pr_test_org_ee1a5ca4_1", "type": "pull_request", "title": "Implement advanced search functionality", "description": "This PR adds comprehensive search capabilities with multiple strategies", "url": "https://github.com/test-org/repo/pull/42", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3341' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 42, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/42', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 27000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_1', 'state': 'open', 'title': 'Implement advanced search functionality', 'body': 'This PR adds comprehensive search capabilities with multiple strategies'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3333' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/test-developer', 'followers_count': 50, 'name': 'Test Developer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 839000000, tzinfo=<UTC>), 'bio': 'Senior software engineer specializing in Python and JavaScript', 'id': 'github_user_test_user_8a3adb3b', 'login': 'test-developer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3335' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/awesome-python-project', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'stars_count': 150, 'html_url': 'https://github.com/test-org/awesome-python-project', 'name': 'awesome-python-project', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 61000000, tzinfo=<UTC>), 'description': 'An awesome Python project with machine learning capabilities', 'language': 'Python', 'id': 'github_repo_test_org_ee1a5ca4_1', 'forks_count': 25}>"}}], "error": null}, {"name": "PRs by code-reviewer for react-dashboard (using filters)", "passed": true, "execution_time": 0.09461402893066406, "total_results": 1, "valid_results": 1, "expected_min_results": 1, "expected_author": "code-reviewer", "expected_repo": "react-dashboard", "success": true, "sample_valid_results": [{"id": "github_pr_test_org_ee1a5ca4_2", "type": "pull_request", "title": "Fix TypeScript compilation issues", "description": "Resolves compilation errors in the React dashboard components", "url": "https://github.com/test-org/repo/pull/43", "score": 1.0, "metadata": {"entity_data": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3342' labels=frozenset({'GitHubPullRequest'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'number': 43, 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 158000000, tzinfo=<UTC>), 'html_url': 'https://github.com/test-org/repo/pull/43', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 25, 158000000, tzinfo=<UTC>), 'id': 'github_pr_test_org_ee1a5ca4_2', 'state': 'closed', 'title': 'Fix TypeScript compilation issues', 'body': 'Resolves compilation errors in the React dashboard components'}>", "related_entities": [], "owner": null, "author": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3334' labels=frozenset({'GitHubUser'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'following_count': 30, 'html_url': 'https://github.com/code-reviewer', 'followers_count': 50, 'name': 'Code Reviewer', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 23, 969000000, tzinfo=<UTC>), 'bio': 'Code review specialist and DevOps engineer', 'id': 'github_user_test_user_8a3adb3b_2', 'login': 'code-reviewer', 'email': '<EMAIL>'}>", "repository": "<Node element_id='4:b33b810d-d7cc-4c6c-9f1a-a9614b4b965a:3336' labels=frozenset({'GitHubRepository'}) properties={'organisation_id': 'test_org_ee1a5ca4', 'private': False, 'full_name': 'test-org/react-dashboard', 'updated_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'stars_count': 89, 'html_url': 'https://github.com/test-org/react-dashboard', 'name': 'react-dashboard', 'created_at': neo4j.time.DateTime(2025, 7, 23, 12, 57, 24, 190000000, tzinfo=<UTC>), 'description': 'Modern React dashboard with TypeScript and Material-UI', 'language': 'TypeScript', 'id': 'github_repo_test_org_ee1a5ca4_2', 'forks_count': 12}>"}}], "error": null}]}