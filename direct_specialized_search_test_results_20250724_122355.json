{"mapping_verification": true, "test_results": {"total_tests": 3, "passed_tests": 3, "failed_tests": 0, "test_details": [{"name": "Direct Repository Specialized Search", "passed": true, "execution_time": 0.24449801445007324, "results_count": 0, "expected_min_results": 0, "strategy_used": "specialized_repository_failed", "success": false, "service_execution_time_ms": 244.46499999999997, "sample_results": [], "error": "{code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.fulltext.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such fulltext schema index: githubRepositoryIndex}", "metadata": {}}, {"name": "Direct User Specialized Search", "passed": true, "execution_time": 0.22760987281799316, "results_count": 0, "expected_min_results": 0, "strategy_used": "specialized_user_failed", "success": false, "service_execution_time_ms": 227.585, "sample_results": [], "error": "{code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.fulltext.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such fulltext schema index: githubUserIndex}", "metadata": {}}, {"name": "Direct Issue Specialized Search", "passed": true, "execution_time": 0.31444621086120605, "results_count": 0, "expected_min_results": 0, "strategy_used": "specialized_issue_failed", "success": false, "service_execution_time_ms": 314.402, "sample_results": [], "error": "{code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.fulltext.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such fulltext schema index: githubIssueIndex}", "metadata": {}}]}}