{"total_tests": 16, "passed_tests": 13, "failed_tests": 3, "test_details": [{"name": "Repository Search - Name", "passed": true, "execution_time": 0.5919129848480225, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 254.05499999999998, "sample_results": [{"id": "github_repo_test_org_62da0239_1", "title": "enhanced-search-engine", "description": "Advanced search engine with comprehensive GitHub schema support", "type": "unknown", "url": "https://github.com/test-org/enhanced-search-engine", "score": 1.0, "metadata": {"entity_type": ["GitHubRepository"], "updated_at": "2025-07-24T04:59:46.393000000+00:00", "created_at": "2025-07-24T04:59:46.393000000+00:00"}}], "error": null}, {"name": "Repository Search - Description", "passed": true, "execution_time": 0.42963099479675293, "results_count": 2, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 190.399, "sample_results": [{"id": "github_repo_test_org_62da0239_1", "title": "enhanced-search-engine", "description": "Advanced search engine with comprehensive GitHub schema support", "type": "unknown", "url": "https://github.com/test-org/enhanced-search-engine", "score": 0.6, "metadata": {"entity_type": ["GitHubRepository"], "updated_at": "2025-07-24T04:59:46.393000000+00:00", "created_at": "2025-07-24T04:59:46.393000000+00:00"}}], "error": null}, {"name": "User Search - Login", "passed": true, "execution_time": 0.2995748519897461, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 113.45700000000001, "sample_results": [{"id": "github_user_test_user_e68160fe", "title": "Enhan<PERSON>", "description": "", "type": "unknown", "url": "https://github.com/enhanced-developer", "score": 0.0, "metadata": {"entity_type": ["GitHubUser"], "updated_at": "2025-07-24T04:59:46.130000000+00:00", "created_at": "2025-07-24T04:59:46.130000000+00:00"}}], "error": null}, {"name": "User Search - Bio", "passed": false, "execution_time": 0.2558131217956543, "results_count": 0, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 87.321, "sample_results": [], "error": null}, {"name": "Issue Search - Title", "passed": true, "execution_time": 0.2635681629180908, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 92.636, "sample_results": [{"id": "github_issue_test_org_62da0239_1", "title": "Implement comprehensive schema support", "description": "Add support for all GitHub entity types defined in the schema YAML file", "type": "unknown", "url": "https://github.com/test-org/repo/issues/101", "score": 0.0, "metadata": {"entity_type": ["GitHubIssue"], "updated_at": "2025-07-24T04:59:46.605000000+00:00", "created_at": "2025-07-24T04:59:46.605000000+00:00"}}], "error": null}, {"name": "Issue Search - Body", "passed": true, "execution_time": 0.26492905616760254, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 90.751, "sample_results": [{"id": "github_issue_test_org_62da0239_1", "title": "Implement comprehensive schema support", "description": "Add support for all GitHub entity types defined in the schema YAML file", "type": "unknown", "url": "https://github.com/test-org/repo/issues/101", "score": 0.0, "metadata": {"entity_type": ["GitHubIssue"], "updated_at": "2025-07-24T04:59:46.605000000+00:00", "created_at": "2025-07-24T04:59:46.605000000+00:00"}}], "error": null}, {"name": "Pull Request Search - Title", "passed": true, "execution_time": 0.27281999588012695, "results_count": 2, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 95.898, "sample_results": [{"id": "github_pr_test_org_62da0239_1", "title": "Add enhanced search capabilities", "description": "This PR implements comprehensive search support for all GitHub entity types", "type": "unknown", "url": "https://github.com/test-org/repo/pull/201", "score": 0.0, "metadata": {"entity_type": ["GitHubPullRequest"], "updated_at": "2025-07-24T04:59:46.977000000+00:00", "created_at": "2025-07-24T04:59:46.977000000+00:00"}}], "error": null}, {"name": "Commit Search - Message", "passed": false, "execution_time": 0.257598876953125, "results_count": 0, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 89.46900000000001, "sample_results": [], "error": null}, {"name": "Branch Search - Name", "passed": true, "execution_time": 0.2529289722442627, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 87.931, "sample_results": [{"id": "github_branch_test_org_62da0239_2", "title": "feature/enhanced-search", "description": "", "type": "unknown", "url": null, "score": 1.0, "metadata": {"entity_type": ["GitHubBranch"], "updated_at": "2025-07-24T04:59:47.478000000+00:00", "created_at": "2025-07-24T04:59:47.478000000+00:00"}}], "error": null}, {"name": "Tag Search - Name", "passed": true, "execution_time": 0.34085512161254883, "results_count": 2, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 173.93200000000002, "sample_results": [{"id": "github_tag_test_org_62da0239_1", "title": "v1.0.0", "description": "", "type": "unknown", "url": null, "score": 1.0, "metadata": {"entity_type": ["GitHubTag"], "updated_at": null, "created_at": "2025-07-24T04:59:47.672000000+00:00"}}], "error": null}, {"name": "Release Search - Name", "passed": true, "execution_time": 0.24790287017822266, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 84.37400000000001, "sample_results": [{"id": "github_release_test_org_62da0239_1", "title": "Enhanced Search Engine v1.0.0", "description": "Major release featuring comprehensive GitHub schema support and enhanced search capabilities", "type": "unknown", "url": "https://github.com/test-org/repo/releases/tag/v1.0.0", "score": 0.8, "metadata": {"entity_type": ["GitHubRelease"], "updated_at": null, "created_at": "2025-07-24T04:59:47.796000000+00:00"}}], "error": null}, {"name": "Code File Search - Path", "passed": true, "execution_time": 0.2756311893463135, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 88.402, "sample_results": [{"id": "github_file_test_org_62da0239_1", "title": "enhanced_engine.py", "description": "", "type": "unknown", "url": "https://github.com/test-org/repo/blob/main/src/search/enhanced_engine.py", "score": 1.0, "metadata": {"entity_type": ["GitHubCodeFile"], "updated_at": "2025-07-24T04:59:47.940000000+00:00", "created_at": "2025-07-24T04:59:47.940000000+00:00"}}], "error": null}, {"name": "Directory Search - Path", "passed": false, "execution_time": 0.250866174697876, "results_count": 0, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 83.05999999999999, "sample_results": [], "error": null}, {"name": "Comment Search - Body", "passed": true, "execution_time": 0.3541843891143799, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 193.304, "sample_results": [{"id": "github_comment_test_org_62da0239_1", "title": null, "description": "Great implementation! The schema-driven approach is very elegant.", "type": "unknown", "url": "https://github.com/test-org/repo/issues/101#comment-github_comment_test_org_62da0239_1", "score": 0.0, "metadata": {"entity_type": ["GitHubComment"], "updated_at": "2025-07-24T04:59:48.299000000+00:00", "created_at": "2025-07-24T04:59:48.299000000+00:00"}}], "error": null}, {"name": "Team Search - Name", "passed": true, "execution_time": 0.2584390640258789, "results_count": 1, "expected_min_results": 1, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 87.039, "sample_results": [{"id": "github_team_test_org_62da0239_1", "title": "Search Team", "description": "Team responsible for search engine development and maintenance", "type": "unknown", "url": "https://github.com/orgs/test-org/teams/search-team", "score": 1.0, "metadata": {"entity_type": ["GitHubTeam"], "updated_at": "2025-07-24T04:59:48.474000000+00:00", "created_at": "2025-07-24T04:59:48.474000000+00:00"}}], "error": null}, {"name": "Generic Search - All Types", "passed": true, "execution_time": 0.24803829193115234, "results_count": 6, "expected_min_results": 3, "strategy_used": "simple_property_fallback", "success": true, "service_execution_time_ms": 90.637, "sample_results": [{"id": "github_release_test_org_62da0239_1", "title": "Enhanced Search Engine v1.0.0", "description": "Major release featuring comprehensive GitHub schema support and enhanced search capabilities", "type": "unknown", "url": "https://github.com/test-org/repo/releases/tag/v1.0.0", "score": 0.8, "metadata": {"entity_type": ["GitHubRelease"], "updated_at": null, "created_at": "2025-07-24T04:59:47.796000000+00:00"}}], "error": null}]}