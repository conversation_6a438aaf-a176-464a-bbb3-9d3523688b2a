"""
GitHub Advanced Search Engine

Implements structured search capabilities for GitHub data following enterprise KG patterns.
Supports multiple search strategies: graph traversal, vector search, hybrid search, and fallback.
"""

import logging
import asyncio
import hashlib
import json
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

from app.services.neo4j_service import execute_read_query, execute_write_query
from app.utils.search.search_schemas import SearchStrategy, SearchQuery, SearchResult
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.redis.redis_service import RedisService

logger = logging.getLogger(__name__)

class GitHubSearchType(Enum):
    """GitHub-specific search types"""
    REPOSITORY = "repository"
    CODE = "code"
    ISSUE = "issue"
    PULL_REQUEST = "pull_request"
    COMMIT = "commit"
    USER = "user"
    ORGANIZATION = "organization"
    SEMANTIC = "semantic"
    GRAPH_TRAVERSAL = "graph_traversal"

class GitHubSearchEngine:
    """Advanced GitHub search engine with multiple strategies"""
    
    def __init__(self, pinecone_service: PineconeService = None, redis_service: RedisService = None):
        self.pinecone_service = pinecone_service
        self.redis_service = redis_service
        self.cache_ttl = 300  # 5 minutes
        
        # GitHub entity type mappings
        self.entity_types = {
            'repository': 'GitHubRepository',
            'issue': 'GitHubIssue', 
            'pull_request': 'GitHubPullRequest',
            'commit': 'GitHubCommit',
            'user': 'GitHubUser',
            'organization': 'GitHubOrganization',
            'file': 'GitHubFile',
            'team': 'GitHubTeam'
        }
        
        # Relationship mappings for graph traversal
        self.relationship_types = {
            'owns': 'OWNS_REPOSITORY',
            'contributes': 'CONTRIBUTES_TO',
            'assigned': 'ASSIGNED_TO',
            'authored': 'AUTHORED_BY',
            'reviewed': 'REVIEWED_BY',
            'member_of': 'MEMBER_OF',
            'belongs_to': 'BELONGS_TO'
        }

    async def search(self, query: str, organisation_id: str, search_type: GitHubSearchType = None, 
                    strategy: SearchStrategy = SearchStrategy.HYBRID, limit: int = 20,
                    filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main search entry point with intelligent strategy selection
        """
        try:
            # Cache key for results
            cache_key = self._generate_cache_key(query, organisation_id, search_type, strategy, filters)
            
            # Check cache first
            if self.redis_service:
                cached_result = self.redis_service.get(cache_key)
                if cached_result:
                    return json.loads(cached_result)
            
            # Determine optimal search strategy if not specified
            if strategy == SearchStrategy.HYBRID:
                strategy = await self._determine_optimal_strategy(query, search_type, filters)
            
            # Execute search based on strategy
            if strategy == SearchStrategy.GRAPH_TRAVERSAL:
                result = await self._graph_traversal_search(query, organisation_id, search_type, filters, limit)
            elif strategy == SearchStrategy.SEMANTIC_ONLY:
                result = await self._vector_search(query, organisation_id, search_type, filters, limit)
            elif strategy == SearchStrategy.ENTITY_CENTRIC:
                result = await self._entity_centric_search(query, organisation_id, search_type, filters, limit)
            elif strategy == SearchStrategy.RELATIONSHIP_CENTRIC:
                result = await self._relationship_centric_search(query, organisation_id, search_type, filters, limit)
            else:
                result = await self._hybrid_search(query, organisation_id, search_type, filters, limit)
            
            # Cache result
            if self.redis_service and result.get('success', False):
                self.redis_service.setex(cache_key, self.cache_ttl, json.dumps(result))
            
            return result
            
        except Exception as e:
            logger.error(f"Error in GitHub search: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _determine_optimal_strategy(self, query: str, search_type: GitHubSearchType, 
                                        filters: Dict[str, Any]) -> SearchStrategy:
        """Intelligently determine the best search strategy"""
        
        # Analyze query characteristics
        has_structured_terms = any(term in query.lower() for term in 
                                 ['repository:', 'author:', 'assignee:', 'label:', 'milestone:'])
        has_semantic_terms = len(query.split()) > 3 and not has_structured_terms
        has_entity_focus = any(entity in query.lower() for entity in 
                             ['user', 'repo', 'organization', 'team'])
        has_relationship_focus = any(rel in query.lower() for rel in 
                                   ['assigned to', 'authored by', 'reviewed by', 'member of'])
        
        # Strategy selection logic
        if has_structured_terms or search_type in [GitHubSearchType.REPOSITORY, GitHubSearchType.USER]:
            return SearchStrategy.GRAPH_TRAVERSAL
        elif has_relationship_focus:
            return SearchStrategy.RELATIONSHIP_CENTRIC
        elif has_entity_focus:
            return SearchStrategy.ENTITY_CENTRIC
        elif has_semantic_terms:
            return SearchStrategy.SEMANTIC_ONLY
        else:
            return SearchStrategy.HYBRID

    async def _graph_traversal_search(self, query: str, organisation_id: str, 
                                    search_type: GitHubSearchType, filters: Dict[str, Any], 
                                    limit: int) -> Dict[str, Any]:
        """Execute structured graph traversal search"""
        start_time = datetime.now()
        
        try:
            # Build Cypher query based on search type and filters
            cypher_query, params = self._build_graph_query(query, organisation_id, search_type, filters, limit)
            
            # Execute query with timeout
            results = execute_read_query(cypher_query, params)
            
            # Process and rank results
            processed_results = self._process_graph_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'graph_traversal',
                'results': processed_results[:limit],
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'metadata': {'cypher_query': cypher_query}
            }
            
        except Exception as e:
            logger.error(f"Graph traversal search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _vector_search(self, query: str, organisation_id: str, 
                           search_type: GitHubSearchType, filters: Dict[str, Any], 
                           limit: int) -> Dict[str, Any]:
        """Execute semantic vector search"""
        start_time = datetime.now()
        
        try:
            if not self.pinecone_service:
                return await self._fallback_search(query, organisation_id, limit)
            
            # Enhance query with GitHub context
            enhanced_query = self._enhance_query_for_github(query, search_type)
            
            # Execute vector search
            success, message, vector_results = self.pinecone_service.vector_search_only(
                query_text=enhanced_query,
                file_ids=None,  # Search all GitHub content
                top_k=limit * 2  # Get more for filtering
            )
            
            if not success:
                return await self._fallback_search(query, organisation_id, limit)
            
            # Filter results for GitHub entities and organisation
            filtered_results = self._filter_vector_results(vector_results, organisation_id, search_type)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'vector_search',
                'results': filtered_results[:limit],
                'total_count': len(filtered_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _entity_centric_search(self, query: str, organisation_id: str, 
                                   search_type: GitHubSearchType, filters: Dict[str, Any], 
                                   limit: int) -> Dict[str, Any]:
        """Execute entity-focused search"""
        start_time = datetime.now()
        
        try:
            # Extract entities from query
            entities = self._extract_entities_from_query(query)
            
            # Build entity-centric query
            cypher_query = """
            MATCH (e)
            WHERE e.organisation_id = $organisation_id
            AND any(label in labels(e) WHERE label IN $entity_types)
            AND (
                any(prop in keys(e) WHERE toString(e[prop]) CONTAINS $query) OR
                any(entity in $entities WHERE 
                    any(prop in keys(e) WHERE toString(e[prop]) CONTAINS entity)
                )
            )
            WITH e, 
                 CASE 
                   WHEN e.name CONTAINS $query THEN 1.0
                   WHEN e.title CONTAINS $query THEN 0.9
                   WHEN e.description CONTAINS $query THEN 0.7
                   ELSE 0.5
                 END as relevance_score
            
            // Expand to related entities
            OPTIONAL MATCH (e)-[r]-(related)
            WHERE related.organisation_id = $organisation_id
            
            RETURN DISTINCT e as entity, 
                   collect(DISTINCT related)[0..5] as related_entities,
                   collect(DISTINCT type(r))[0..5] as relationship_types,
                   relevance_score
            ORDER BY relevance_score DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'entities': entities,
                'entity_types': list(self.entity_types.values()),
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_entity_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'entity_centric',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Entity-centric search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _relationship_centric_search(self, query: str, organisation_id: str, 
                                         search_type: GitHubSearchType, filters: Dict[str, Any], 
                                         limit: int) -> Dict[str, Any]:
        """Execute relationship-focused search"""
        start_time = datetime.now()
        
        try:
            # Extract relationship patterns from query
            relationship_patterns = self._extract_relationship_patterns(query)
            
            cypher_query = """
            MATCH (source)-[r]->(target)
            WHERE source.organisation_id = $organisation_id
            AND target.organisation_id = $organisation_id
            AND (
                type(r) IN $relationship_types OR
                any(pattern in $patterns WHERE type(r) CONTAINS pattern)
            )
            AND (
                any(prop in keys(source) WHERE toString(source[prop]) CONTAINS $query) OR
                any(prop in keys(target) WHERE toString(target[prop]) CONTAINS $query)
            )
            
            WITH source, r, target,
                 CASE 
                   WHEN type(r) IN $primary_relationships THEN 1.0
                   ELSE 0.7
                 END as relationship_score
            
            RETURN source, type(r) as relationship_type, target, relationship_score
            ORDER BY relationship_score DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'relationship_types': list(self.relationship_types.values()),
                'patterns': relationship_patterns,
                'primary_relationships': ['AUTHORED_BY', 'ASSIGNED_TO', 'OWNS_REPOSITORY'],
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_relationship_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'relationship_centric',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Relationship-centric search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _hybrid_search(self, query: str, organisation_id: str, 
                           search_type: GitHubSearchType, filters: Dict[str, Any], 
                           limit: int) -> Dict[str, Any]:
        """Execute hybrid search combining multiple strategies"""
        start_time = datetime.now()
        
        try:
            # Run multiple searches concurrently
            tasks = [
                self._graph_traversal_search(query, organisation_id, search_type, filters, limit // 2),
                self._vector_search(query, organisation_id, search_type, filters, limit // 2)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Merge and rank results
            merged_results = self._merge_search_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'hybrid',
                'results': merged_results[:limit],
                'total_count': len(merged_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _fallback_search(self, query: str, organisation_id: str, limit: int) -> Dict[str, Any]:
        """
        Fallback search using Neo4j full-text indexes when other strategies fail
        """
        start_time = datetime.now()
        
        try:
            # Use full-text search across all GitHub entities
            cypher_query = """
            CALL db.index.fulltext.queryNodes('githubUnifiedIndex', $search_query) 
            YIELD node, score
            WHERE node.organisation_id = $organisation_id
            
            // Boost score based on entity type and property matches
            WITH node, score,
                 CASE 
                   WHEN 'GitHubRepository' IN labels(node) THEN score * 1.2
                   WHEN 'GitHubIssue' IN labels(node) THEN score * 1.1  
                   WHEN 'GitHubPullRequest' IN labels(node) THEN score * 1.1
                   WHEN 'GitHubUser' IN labels(node) THEN score * 1.0
                   ELSE score * 0.9
                 END as boosted_score
            
            // Get related entities for context
            OPTIONAL MATCH (node)-[r]-(related)
            WHERE related.organisation_id = $organisation_id
            
            RETURN DISTINCT node as entity, 
                   labels(node) as entity_types,
                   boosted_score as relevance_score,
                   collect(DISTINCT related)[0..3] as related_entities,
                   collect(DISTINCT type(r))[0..3] as relationship_types
            ORDER BY boosted_score DESC
            LIMIT $limit
            """
            
            # Prepare search query with proper escaping
            search_query = self._prepare_fulltext_query(query)
            
            params = {
                'search_query': search_query,
                'organisation_id': organisation_id,
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_fulltext_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'fulltext_fallback',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'metadata': {
                    'search_query': search_query,
                    'index_used': 'githubUnifiedIndex'
                }
            }
            
        except Exception as e:
            logger.error(f"Full-text fallback search failed: {str(e)}")
            
            # Ultimate fallback - simple property search
            return await self._simple_property_fallback(query, organisation_id, limit)

    async def _simple_property_fallback(self, query: str, organisation_id: str, limit: int) -> Dict[str, Any]:
        """
        Ultimate fallback using simple property search when full-text indexes fail
        """
        start_time = datetime.now()
        
        try:
            cypher_query = """
            MATCH (n)
            WHERE n.organisation_id = $organisation_id
            AND any(label in labels(n) WHERE label STARTS WITH 'GitHub')
            AND (
                (n.name IS NOT NULL AND toLower(n.name) CONTAINS $query) OR
                (n.title IS NOT NULL AND toLower(n.title) CONTAINS $query) OR
                (n.description IS NOT NULL AND toLower(n.description) CONTAINS $query) OR
                (n.body IS NOT NULL AND toLower(n.body) CONTAINS $query) OR
                (n.login IS NOT NULL AND toLower(n.login) CONTAINS $query)
            )
            
            WITH n, 
                 CASE 
                   WHEN n.name IS NOT NULL AND toLower(n.name) CONTAINS $query THEN 1.0
                   WHEN n.title IS NOT NULL AND toLower(n.title) CONTAINS $query THEN 0.9
                   WHEN n.login IS NOT NULL AND toLower(n.login) CONTAINS $query THEN 0.8
                   WHEN n.description IS NOT NULL AND toLower(n.description) CONTAINS $query THEN 0.7
                   WHEN n.body IS NOT NULL AND toLower(n.body) CONTAINS $query THEN 0.6
                   ELSE 0.5
                 END as relevance_score
            
            RETURN n as entity, labels(n) as entity_types, relevance_score
            ORDER BY relevance_score DESC, n.updated_at DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_fallback_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'simple_property_fallback',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'warning': 'Used simple property fallback due to full-text index failure'
            }
            
        except Exception as e:
            logger.error(f"Simple property fallback failed: {str(e)}")
            return {
                'success': False,
                'strategy': 'fallback_failed',
                'results': [],
                'total_count': 0,
                'execution_time_ms': (datetime.now() - start_time).total_seconds() * 1000,
                'error': str(e)
            }

    def _prepare_fulltext_query(self, query: str) -> str:
        """
        Prepare query for Neo4j full-text search with proper escaping and operators
        """
        # Clean and escape the query
        cleaned_query = query.strip()
        
        # Handle special characters that need escaping in Lucene
        special_chars = [':', '(', ')', '[', ']', '{', '}', '~', '^', '"', '\\']
        for char in special_chars:
            cleaned_query = cleaned_query.replace(char, f'\\{char}')
        
        # Split into words and create search terms
        words = cleaned_query.split()
        
        if len(words) == 1:
            # Single word - use fuzzy matching
            return f"{words[0]}~0.8 OR {words[0]}*"
        elif len(words) <= 3:
            # Few words - use AND with fuzzy matching
            fuzzy_terms = [f"{word}~0.8" for word in words]
            exact_terms = [f"{word}*" for word in words]
            return f"({' AND '.join(fuzzy_terms)}) OR ({' AND '.join(exact_terms)})"
        else:
            # Many words - use phrase search with some flexibility
            phrase = ' '.join(words)
            return f'"{phrase}"~3 OR ({" OR ".join(f"{word}*" for word in words)})'

    def _process_fulltext_results(self, results: List[Dict], query: str) -> List[Dict]:
        """
        Process full-text search results with enhanced metadata
        """
        processed = []
        
        for result in results:
            entity = result.get('entity', {})
            entity_types = result.get('entity_types', [])
            relevance_score = result.get('relevance_score', 0.0)
            related_entities = result.get('related_entities', [])
            relationship_types = result.get('relationship_types', [])
            
            # Determine primary entity type
            primary_type = 'unknown'
            for label in entity_types:
                if label.startswith('GitHub'):
                    primary_type = label.replace('GitHub', '').lower()
                    break
            
            processed_result = {
                'id': entity.get('id') or entity.get('github_id'),
                'title': entity.get('name') or entity.get('title') or entity.get('login'),
                'description': self._get_entity_description(entity, primary_type),
                'type': primary_type,
                'url': entity.get('html_url') or entity.get('url'),
                'score': float(relevance_score),
                'metadata': {
                    'entity_types': entity_types,
                    'updated_at': entity.get('updated_at'),
                    'created_at': entity.get('created_at'),
                    'related_count': len(related_entities),
                    'relationship_types': relationship_types,
                    'search_method': 'fulltext'
                }
            }
            processed.append(processed_result)
        
        return processed

    def _get_entity_description(self, entity: Dict, entity_type: str) -> str:
        """
        Get appropriate description based on entity type
        """
        if entity_type == 'repository':
            return entity.get('description', '')[:200]
        elif entity_type in ['issue', 'pullrequest']:
            return entity.get('body', '')[:200]
        elif entity_type == 'user':
            bio = entity.get('bio', '')
            company = entity.get('company', '')
            return f"{bio} {company}".strip()[:200]
        elif entity_type == 'commit':
            return entity.get('message', '')[:200]
        elif entity_type in ['file', 'codefile']:
            return f"File: {entity.get('path', '')} ({entity.get('extension', '')})"
        else:
            return entity.get('description', '') or entity.get('body', '') or ''

    def _build_graph_query(self, query: str, organisation_id: str, 
                          search_type: GitHubSearchType, filters: Dict[str, Any], 
                          limit: int) -> Tuple[str, Dict]:
        """Build optimized Cypher query based on search type"""
        
        base_conditions = "n.organisation_id = $organisation_id"
        params = {'organisation_id': organisation_id, 'limit': limit}
        
        if search_type == GitHubSearchType.REPOSITORY:
            cypher_query = f"""
            MATCH (n:GitHubRepository)
            WHERE {base_conditions}
            AND (n.name CONTAINS $query OR n.description CONTAINS $query)
            OPTIONAL MATCH (n)-[:OWNED_BY]->(owner:GitHubUser)
            RETURN n as entity, owner, 'repository' as type
            ORDER BY n.stars_count DESC, n.updated_at DESC
            LIMIT $limit
            """
            params['query'] = query.lower()
            
        elif search_type == GitHubSearchType.ISSUE:
            cypher_query = f"""
            MATCH (n:GitHubIssue)
            WHERE {base_conditions}
            AND (n.title CONTAINS $query OR n.body CONTAINS $query)
            OPTIONAL MATCH (n)-[:BELONGS_TO]->(repo:GitHubRepository)
            OPTIONAL MATCH (n)-[:ASSIGNED_TO]->(assignee:GitHubUser)
            RETURN n as entity, repo, assignee, 'issue' as type
            ORDER BY n.updated_at DESC
            LIMIT $limit
            """
            params['query'] = query.lower()
            
        else:
            # Generic search across all GitHub entities
            cypher_query = f"""
            MATCH (n)
            WHERE {base_conditions}
            AND any(label in labels(n) WHERE label STARTS WITH 'GitHub')
            AND any(prop in keys(n) WHERE toString(n[prop]) CONTAINS $query)
            RETURN n as entity, labels(n) as entity_types, 'generic' as type
            ORDER BY n.updated_at DESC
            LIMIT $limit
            """
            params['query'] = query.lower()
        
        return cypher_query, params

    def _generate_cache_key(self, query: str, organisation_id: str, 
                           search_type: GitHubSearchType, strategy: SearchStrategy, 
                           filters: Dict[str, Any]) -> str:
        """Generate cache key for search results"""
        key_data = {
            'query': query,
            'organisation_id': organisation_id,
            'search_type': search_type.value if search_type else None,
            'strategy': strategy.value,
            'filters': filters or {}
        }
        return f"github_search:{hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()}"

    def _extract_entities_from_query(self, query: str) -> List[str]:
        """Extract potential entity names from query"""
        # Simple entity extraction - can be enhanced with NLP
        words = query.split()
        entities = []
        
        # Look for capitalized words (potential proper nouns)
        for word in words:
            if word[0].isupper() and len(word) > 2:
                entities.append(word.lower())
        
        return entities

    def _extract_relationship_patterns(self, query: str) -> List[str]:
        """Extract relationship patterns from query"""
        patterns = []
        query_lower = query.lower()
        
        relationship_keywords = {
            'assigned': 'ASSIGNED',
            'authored': 'AUTHORED',
            'reviewed': 'REVIEWED',
            'owns': 'OWNS',
            'member': 'MEMBER',
            'belongs': 'BELONGS'
        }
        
        for keyword, pattern in relationship_keywords.items():
            if keyword in query_lower:
                patterns.append(pattern)
        
        return patterns

    def _process_graph_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process and format graph search results"""
        processed = []
        
        for result in results:
            entity = result.get('entity', {})
            processed_result = {
                'id': entity.get('id') or entity.get('github_id'),
                'title': entity.get('name') or entity.get('title'),
                'description': entity.get('description') or entity.get('body', '')[:200],
                'type': result.get('type', 'unknown'),
                'url': entity.get('html_url'),
                'score': self._calculate_relevance_score(entity, query),
                'metadata': {
                    'entity_type': result.get('entity_types', []),
                    'updated_at': entity.get('updated_at'),
                    'created_at': entity.get('created_at')
                }
            }
            processed.append(processed_result)
        
        return sorted(processed, key=lambda x: x['score'], reverse=True)

    def _calculate_relevance_score(self, entity: Dict, query: str) -> float:
        """Calculate relevance score for search results"""
        score = 0.0
        query_lower = query.lower()
        
        # Title/name match
        if entity.get('name', '').lower() == query_lower:
            score += 1.0
        elif query_lower in entity.get('name', '').lower():
            score += 0.8
        
        # Description match
        if query_lower in entity.get('description', '').lower():
            score += 0.6
        
        # Boost for recent updates
        if entity.get('updated_at'):
            try:
                updated = datetime.fromisoformat(entity['updated_at'].replace('Z', '+00:00'))
                days_old = (datetime.now() - updated.replace(tzinfo=None)).days
                if days_old < 30:
                    score += 0.2
            except:
                pass
        
        return min(score, 1.0)

    # Additional helper methods for processing different result types...
    def _process_entity_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process entity-centric search results"""
        # Implementation similar to _process_graph_results but with entity focus
        return self._process_graph_results(results, query)

    def _process_relationship_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process relationship-centric search results"""
        # Implementation for relationship results
        return self._process_graph_results(results, query)

    def _process_fallback_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process fallback search results"""
        return self._process_graph_results(results, query)

    def _merge_search_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Merge results from multiple search strategies"""
        merged = []
        seen_ids = set()
        
        for result_set in results:
            if isinstance(result_set, dict) and result_set.get('success'):
                for item in result_set.get('results', []):
                    item_id = item.get('id')
                    if item_id and item_id not in seen_ids:
                        seen_ids.add(item_id)
                        merged.append(item)
        
        return sorted(merged, key=lambda x: x.get('score', 0), reverse=True)

    def _enhance_query_for_github(self, query: str, search_type: GitHubSearchType) -> str:
        """Enhance query with GitHub-specific context"""
        if search_type == GitHubSearchType.CODE:
            return f"GitHub code: {query}"
        elif search_type == GitHubSearchType.ISSUE:
            return f"GitHub issue: {query}"
        elif search_type == GitHubSearchType.REPOSITORY:
            return f"GitHub repository: {query}"
        return f"GitHub: {query}"

    def _filter_vector_results(self, results: List[Dict], organisation_id: str, 
                              search_type: GitHubSearchType) -> List[Dict]:
        """Filter vector search results for GitHub entities"""
        filtered = []
        
        for result in results:
            metadata = result.get('metadata', {})
            if (metadata.get('organisation_id') == organisation_id and 
                metadata.get('source_type') == 'github'):
                filtered.append({
                    'id': result.get('id'),
                    'title': metadata.get('title', ''),
                    'description': result.get('content', '')[:200],
                    'type': metadata.get('entity_type', 'unknown'),
                    'url': metadata.get('url'),
                    'score': result.get('score', 0.0),
                    'metadata': metadata
                })
        
        return filtered

    async def _specialized_entity_search(self, query: str, organisation_id: str, 
                                       search_type: GitHubSearchType, limit: int) -> Dict[str, Any]:
        """
        Execute specialized search using entity-specific full-text indexes
        """
        start_time = datetime.now()
        
        try:
            # Map search type to index name
            index_mapping = {
                GitHubSearchType.REPOSITORY: 'githubRepositoryIndex',
                GitHubSearchType.ISSUE: 'githubIssueIndex',
                GitHubSearchType.PULL_REQUEST: 'githubPullRequestIndex',
                GitHubSearchType.CODE: 'githubCodeFileIndex',
                GitHubSearchType.USER: 'githubUserIndex',
                GitHubSearchType.COMMIT: 'githubCommitIndex'
            }
            
            index_name = index_mapping.get(search_type, 'githubUnifiedIndex')
            search_query = self._prepare_fulltext_query(query)
            
            cypher_query = f"""
            CALL db.index.fulltext.queryNodes('{index_name}', $search_query) 
            YIELD node, score
            WHERE node.organisation_id = $organisation_id
            
            // Get additional context based on entity type
            OPTIONAL MATCH (node)-[r]-(related)
            WHERE related.organisation_id = $organisation_id
            
            RETURN node as entity, 
                   labels(node) as entity_types,
                   score as relevance_score,
                   collect(DISTINCT related)[0..5] as related_entities,
                   collect(DISTINCT type(r))[0..5] as relationship_types
            ORDER BY score DESC
            LIMIT $limit
            """
            
            params = {
                'search_query': search_query,
                'organisation_id': organisation_id,
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_fulltext_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': f'specialized_{search_type.value}',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'metadata': {
                    'index_used': index_name,
                    'search_query': search_query
                }
            }
            
        except Exception as e:
            logger.error(f"Specialized entity search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)
