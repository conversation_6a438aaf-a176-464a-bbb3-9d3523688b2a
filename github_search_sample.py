"""
Enhanced GitHub Advanced Search Engine

Implements comprehensive search capabilities for GitHub data with full schema support.
Supports all GitHub node types and relationships defined in the schema YAML file.
Features: graph traversal, vector search, hybrid search, schema-driven queries, and fallback.
"""

import logging
import asyncio
import hashlib
import json
import yaml
import os
from typing import List, Dict, Any, Optional, Set, Tuple, Union
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path

from app.services.neo4j_service import execute_read_query, execute_write_query
from app.utils.search.search_schemas import SearchStrategy, SearchQuery, SearchResult
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.redis.redis_service import RedisService

logger = logging.getLogger(__name__)

class GitHubSearchType(Enum):
    """GitHub-specific search types - supports all node types from schema"""
    # Core entities
    REPOSITORY = "repository"
    USER = "user"
    ORGANIZATION = "organization"

    # Content entities
    CODE_FILE = "code_file"
    DIRECTORY = "directory"
    ISSUE = "issue"
    PULL_REQUEST = "pull_request"
    COMMIT = "commit"
    BRANCH = "branch"
    TAG = "tag"
    RELEASE = "release"

    # Social entities
    COMMENT = "comment"
    REVIEW = "review"
    REVIEW_COMMENT = "review_comment"
    TEAM = "team"

    # Special search types
    ALL = "all"
    SEMANTIC = "semantic"
    GRAPH_TRAVERSAL = "graph_traversal"

class GitHubSchemaLoader:
    """Loads and parses GitHub schema YAML file"""

    def __init__(self, schema_path: str = None):
        if schema_path is None:
            # Default path to schema file
            schema_path = "app/modules/connectors/handlers/github/models/github_schema.yml"

        self.schema_path = schema_path
        self.schema_data = None
        self.entity_types = {}
        self.relationship_types = {}
        self.node_properties = {}
        self.relationship_properties = {}

        self._load_schema()

    def _load_schema(self):
        """Load and parse the GitHub schema YAML file"""
        try:
            if os.path.exists(self.schema_path):
                with open(self.schema_path, 'r') as file:
                    self.schema_data = yaml.safe_load(file)
                    self._parse_schema()
            else:
                logger.warning(f"Schema file not found at {self.schema_path}, using fallback mappings")
                self._load_fallback_mappings()
        except Exception as e:
            logger.error(f"Error loading schema: {str(e)}, using fallback mappings")
            self._load_fallback_mappings()

    def _parse_schema(self):
        """Parse schema data into usable mappings"""
        if not self.schema_data:
            return

        # Parse node types
        nodes = self.schema_data.get('nodes', {})
        for node_name, node_config in nodes.items():
            # Map search type to Neo4j label
            search_type = node_name.replace('GitHub', '').lower()
            if search_type == 'codefile':
                search_type = 'code_file'
            elif search_type == 'pullrequest':
                search_type = 'pull_request'
            elif search_type == 'reviewcomment':
                search_type = 'review_comment'

            self.entity_types[search_type] = node_name
            self.node_properties[node_name] = node_config.get('properties', {})

        # Parse relationships
        relationships = self.schema_data.get('relationships', {})
        for rel_name, rel_config in relationships.items():
            # Create mapping from relationship name to Neo4j type
            self.relationship_types[rel_name.lower()] = rel_name
            self.relationship_properties[rel_name] = rel_config.get('properties', {})

    def _load_fallback_mappings(self):
        """Load fallback mappings when schema file is not available"""
        self.entity_types = {
            'repository': 'GitHubRepository',
            'user': 'GitHubUser',
            'organization': 'GitHubOrganization',
            'code_file': 'GitHubCodeFile',
            'directory': 'GitHubDirectory',
            'issue': 'GitHubIssue',
            'pull_request': 'GitHubPullRequest',
            'commit': 'GitHubCommit',
            'branch': 'GitHubBranch',
            'tag': 'GitHubTag',
            'release': 'GitHubRelease',
            'comment': 'GitHubComment',
            'review': 'GitHubReview',
            'review_comment': 'GitHubReviewComment',
            'team': 'GitHubTeam'
        }

        self.relationship_types = {
            'owns_repository': 'OWNS_REPOSITORY',
            'contributes_to': 'CONTRIBUTES_TO',
            'assigned_to': 'ASSIGNED_TO',
            'authored_by': 'AUTHORED_BY',
            'reviewed_by': 'REVIEWED_BY',
            'member_of': 'MEMBER_OF',
            'belongs_to': 'BELONGS_TO',
            'creates_issue': 'CREATES_ISSUE',
            'creates_pull_request': 'CREATES_PULL_REQUEST',
            'merges_pull_request': 'MERGES_PULL_REQUEST',
            'comments_on': 'COMMENTS_ON',
            'reviews_pull_request': 'REVIEWS_PULL_REQUEST'
        }


class GitHubSearchEngine:
    """Enhanced GitHub search engine with comprehensive schema support"""

    def __init__(self, pinecone_service: PineconeService = None, redis_service: RedisService = None,
                 schema_path: str = None):
        self.pinecone_service = pinecone_service
        self.redis_service = redis_service
        self.cache_ttl = 300  # 5 minutes

        # Load schema configuration
        self.schema_loader = GitHubSchemaLoader(schema_path)
        self.entity_types = self.schema_loader.entity_types
        self.relationship_types = self.schema_loader.relationship_types
        self.node_properties = self.schema_loader.node_properties
        self.relationship_properties = self.schema_loader.relationship_properties

    async def search(self, query: str, organisation_id: str, search_type: GitHubSearchType = None, 
                    strategy: SearchStrategy = SearchStrategy.HYBRID, limit: int = 20,
                    filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main search entry point with intelligent strategy selection
        """
        try:
            # Cache key for results
            cache_key = self._generate_cache_key(query, organisation_id, search_type, strategy, filters)
            
            # Check cache first
            if self.redis_service:
                cached_result = self.redis_service.get(cache_key)
                if cached_result:
                    return json.loads(cached_result)
            
            # Determine optimal search strategy if not specified
            if strategy == SearchStrategy.HYBRID:
                strategy = await self._determine_optimal_strategy(query, search_type, filters)
            
            # Execute search based on strategy
            if strategy == SearchStrategy.GRAPH_TRAVERSAL:
                result = await self._graph_traversal_search(query, organisation_id, search_type, filters, limit)
            elif strategy == SearchStrategy.SEMANTIC_ONLY:
                result = await self._vector_search(query, organisation_id, search_type, filters, limit)
            elif strategy == SearchStrategy.ENTITY_CENTRIC:
                result = await self._entity_centric_search(query, organisation_id, search_type, filters, limit)
            elif strategy == SearchStrategy.RELATIONSHIP_CENTRIC:
                result = await self._relationship_centric_search(query, organisation_id, search_type, filters, limit)
            else:
                result = await self._hybrid_search(query, organisation_id, search_type, filters, limit)
            
            # Cache result
            if self.redis_service and result.get('success', False):
                self.redis_service.setex(cache_key, self.cache_ttl, json.dumps(result))
            
            return result
            
        except Exception as e:
            logger.error(f"Error in GitHub search: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _determine_optimal_strategy(self, query: str, search_type: GitHubSearchType, 
                                        filters: Dict[str, Any]) -> SearchStrategy:
        """Intelligently determine the best search strategy"""
        
        # Analyze query characteristics
        has_structured_terms = any(term in query.lower() for term in 
                                 ['repository:', 'author:', 'assignee:', 'label:', 'milestone:'])
        has_semantic_terms = len(query.split()) > 3 and not has_structured_terms
        has_entity_focus = any(entity in query.lower() for entity in 
                             ['user', 'repo', 'organization', 'team'])
        has_relationship_focus = any(rel in query.lower() for rel in 
                                   ['assigned to', 'authored by', 'reviewed by', 'member of'])
        
        # Strategy selection logic
        if has_structured_terms or search_type in [GitHubSearchType.REPOSITORY, GitHubSearchType.USER]:
            return SearchStrategy.GRAPH_TRAVERSAL
        elif has_relationship_focus:
            return SearchStrategy.RELATIONSHIP_CENTRIC
        elif has_entity_focus:
            return SearchStrategy.ENTITY_CENTRIC
        elif has_semantic_terms:
            return SearchStrategy.SEMANTIC_ONLY
        else:
            return SearchStrategy.HYBRID

    async def _graph_traversal_search(self, query: str, organisation_id: str, 
                                    search_type: GitHubSearchType, filters: Dict[str, Any], 
                                    limit: int) -> Dict[str, Any]:
        """Execute structured graph traversal search"""
        start_time = datetime.now()
        
        try:
            # Build Cypher query based on search type and filters
            cypher_query, params = self._build_graph_query(query, organisation_id, search_type, filters, limit)
            
            # Execute query with timeout
            results = execute_read_query(cypher_query, params)
            
            # Process and rank results
            processed_results = self._process_graph_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'graph_traversal',
                'results': processed_results[:limit],
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'metadata': {'cypher_query': cypher_query}
            }
            
        except Exception as e:
            logger.error(f"Graph traversal search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _vector_search(self, query: str, organisation_id: str, 
                           search_type: GitHubSearchType, filters: Dict[str, Any], 
                           limit: int) -> Dict[str, Any]:
        """Execute semantic vector search"""
        start_time = datetime.now()
        
        try:
            if not self.pinecone_service:
                return await self._fallback_search(query, organisation_id, limit)
            
            # Enhance query with GitHub context
            enhanced_query = self._enhance_query_for_github(query, search_type)
            
            # Execute vector search
            success, message, vector_results = self.pinecone_service.vector_search_only(
                query_text=enhanced_query,
                file_ids=None,  # Search all GitHub content
                top_k=limit * 2  # Get more for filtering
            )
            
            if not success:
                return await self._fallback_search(query, organisation_id, limit)
            
            # Filter results for GitHub entities and organisation
            filtered_results = self._filter_vector_results(vector_results, organisation_id, search_type)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'vector_search',
                'results': filtered_results[:limit],
                'total_count': len(filtered_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _entity_centric_search(self, query: str, organisation_id: str, 
                                   search_type: GitHubSearchType, filters: Dict[str, Any], 
                                   limit: int) -> Dict[str, Any]:
        """Execute entity-focused search"""
        start_time = datetime.now()
        
        try:
            # Extract entities from query
            entities = self._extract_entities_from_query(query)
            
            # Build entity-centric query
            cypher_query = """
            MATCH (e)
            WHERE e.organisation_id = $organisation_id
            AND any(label in labels(e) WHERE label IN $entity_types)
            AND (
                any(prop in keys(e) WHERE toString(e[prop]) CONTAINS $query) OR
                any(entity in $entities WHERE 
                    any(prop in keys(e) WHERE toString(e[prop]) CONTAINS entity)
                )
            )
            WITH e, 
                 CASE 
                   WHEN e.name CONTAINS $query THEN 1.0
                   WHEN e.title CONTAINS $query THEN 0.9
                   WHEN e.description CONTAINS $query THEN 0.7
                   ELSE 0.5
                 END as relevance_score
            
            // Expand to related entities
            OPTIONAL MATCH (e)-[r]-(related)
            WHERE related.organisation_id = $organisation_id
            
            RETURN DISTINCT e as entity, 
                   collect(DISTINCT related)[0..5] as related_entities,
                   collect(DISTINCT type(r))[0..5] as relationship_types,
                   relevance_score
            ORDER BY relevance_score DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'entities': entities,
                'entity_types': list(self.entity_types.values()),
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_entity_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'entity_centric',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Entity-centric search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _relationship_centric_search(self, query: str, organisation_id: str, 
                                         search_type: GitHubSearchType, filters: Dict[str, Any], 
                                         limit: int) -> Dict[str, Any]:
        """Execute relationship-focused search"""
        start_time = datetime.now()
        
        try:
            # Extract relationship patterns from query
            relationship_patterns = self._extract_relationship_patterns(query)
            
            cypher_query = """
            MATCH (source)-[r]->(target)
            WHERE source.organisation_id = $organisation_id
            AND target.organisation_id = $organisation_id
            AND (
                type(r) IN $relationship_types OR
                any(pattern in $patterns WHERE type(r) CONTAINS pattern)
            )
            AND (
                any(prop in keys(source) WHERE toString(source[prop]) CONTAINS $query) OR
                any(prop in keys(target) WHERE toString(target[prop]) CONTAINS $query)
            )
            
            WITH source, r, target,
                 CASE 
                   WHEN type(r) IN $primary_relationships THEN 1.0
                   ELSE 0.7
                 END as relationship_score
            
            RETURN source, type(r) as relationship_type, target, relationship_score
            ORDER BY relationship_score DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'relationship_types': list(self.relationship_types.values()),
                'patterns': relationship_patterns,
                'primary_relationships': ['AUTHORED_BY', 'ASSIGNED_TO', 'OWNS_REPOSITORY'],
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_relationship_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'relationship_centric',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Relationship-centric search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _hybrid_search(self, query: str, organisation_id: str, 
                           search_type: GitHubSearchType, filters: Dict[str, Any], 
                           limit: int) -> Dict[str, Any]:
        """Execute hybrid search combining multiple strategies"""
        start_time = datetime.now()
        
        try:
            # Run multiple searches concurrently
            tasks = [
                self._graph_traversal_search(query, organisation_id, search_type, filters, limit // 2),
                self._vector_search(query, organisation_id, search_type, filters, limit // 2)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Merge and rank results
            merged_results = self._merge_search_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'hybrid',
                'results': merged_results[:limit],
                'total_count': len(merged_results),
                'execution_time_ms': execution_time
            }
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    async def _fallback_search(self, query: str, organisation_id: str, limit: int) -> Dict[str, Any]:
        """
        Fallback search using Neo4j full-text indexes when other strategies fail
        """
        start_time = datetime.now()
        
        try:
            # Use full-text search across all GitHub entities
            cypher_query = """
            CALL db.index.fulltext.queryNodes('githubUnifiedIndex', $search_query) 
            YIELD node, score
            WHERE node.organisation_id = $organisation_id
            
            // Boost score based on entity type and property matches
            WITH node, score,
                 CASE 
                   WHEN 'GitHubRepository' IN labels(node) THEN score * 1.2
                   WHEN 'GitHubIssue' IN labels(node) THEN score * 1.1  
                   WHEN 'GitHubPullRequest' IN labels(node) THEN score * 1.1
                   WHEN 'GitHubUser' IN labels(node) THEN score * 1.0
                   ELSE score * 0.9
                 END as boosted_score
            
            // Get related entities for context
            OPTIONAL MATCH (node)-[r]-(related)
            WHERE related.organisation_id = $organisation_id
            
            RETURN DISTINCT node as entity, 
                   labels(node) as entity_types,
                   boosted_score as relevance_score,
                   collect(DISTINCT related)[0..3] as related_entities,
                   collect(DISTINCT type(r))[0..3] as relationship_types
            ORDER BY boosted_score DESC
            LIMIT $limit
            """
            
            # Prepare search query with proper escaping
            search_query = self._prepare_fulltext_query(query)
            
            params = {
                'search_query': search_query,
                'organisation_id': organisation_id,
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_fulltext_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'fulltext_fallback',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'metadata': {
                    'search_query': search_query,
                    'index_used': 'githubUnifiedIndex'
                }
            }
            
        except Exception as e:
            logger.error(f"Full-text fallback search failed: {str(e)}")
            
            # Ultimate fallback - simple property search
            return await self._simple_property_fallback(query, organisation_id, limit)

    async def _simple_property_fallback(self, query: str, organisation_id: str, limit: int) -> Dict[str, Any]:
        """
        Ultimate fallback using simple property search when full-text indexes fail
        """
        start_time = datetime.now()
        
        try:
            cypher_query = """
            MATCH (n)
            WHERE n.organisation_id = $organisation_id
            AND any(label in labels(n) WHERE label STARTS WITH 'GitHub')
            AND (
                (n.name IS NOT NULL AND toLower(n.name) CONTAINS $query) OR
                (n.title IS NOT NULL AND toLower(n.title) CONTAINS $query) OR
                (n.description IS NOT NULL AND toLower(n.description) CONTAINS $query) OR
                (n.body IS NOT NULL AND toLower(n.body) CONTAINS $query) OR
                (n.login IS NOT NULL AND toLower(n.login) CONTAINS $query)
            )
            
            WITH n, 
                 CASE 
                   WHEN n.name IS NOT NULL AND toLower(n.name) CONTAINS $query THEN 1.0
                   WHEN n.title IS NOT NULL AND toLower(n.title) CONTAINS $query THEN 0.9
                   WHEN n.login IS NOT NULL AND toLower(n.login) CONTAINS $query THEN 0.8
                   WHEN n.description IS NOT NULL AND toLower(n.description) CONTAINS $query THEN 0.7
                   WHEN n.body IS NOT NULL AND toLower(n.body) CONTAINS $query THEN 0.6
                   ELSE 0.5
                 END as relevance_score
            
            RETURN n as entity, labels(n) as entity_types, relevance_score
            ORDER BY relevance_score DESC, n.updated_at DESC
            LIMIT $limit
            """
            
            params = {
                'organisation_id': organisation_id,
                'query': query.lower(),
                'limit': limit
            }
            
            results = execute_read_query(cypher_query, params)
            processed_results = self._process_fallback_results(results, query)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return {
                'success': True,
                'strategy': 'simple_property_fallback',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'warning': 'Used simple property fallback due to full-text index failure'
            }
            
        except Exception as e:
            logger.error(f"Simple property fallback failed: {str(e)}")
            return {
                'success': False,
                'strategy': 'fallback_failed',
                'results': [],
                'total_count': 0,
                'execution_time_ms': (datetime.now() - start_time).total_seconds() * 1000,
                'error': str(e)
            }

    def _prepare_fulltext_query(self, query: str) -> str:
        """
        Prepare query for Neo4j full-text search with proper escaping and operators
        """
        # Clean and escape the query
        cleaned_query = query.strip()
        
        # Handle special characters that need escaping in Lucene
        special_chars = [':', '(', ')', '[', ']', '{', '}', '~', '^', '"', '\\']
        for char in special_chars:
            cleaned_query = cleaned_query.replace(char, f'\\{char}')
        
        # Split into words and create search terms
        words = cleaned_query.split()
        
        if len(words) == 1:
            # Single word - use fuzzy matching
            return f"{words[0]}~0.8 OR {words[0]}*"
        elif len(words) <= 3:
            # Few words - use AND with fuzzy matching
            fuzzy_terms = [f"{word}~0.8" for word in words]
            exact_terms = [f"{word}*" for word in words]
            return f"({' AND '.join(fuzzy_terms)}) OR ({' AND '.join(exact_terms)})"
        else:
            # Many words - use phrase search with some flexibility
            phrase = ' '.join(words)
            return f'"{phrase}"~3 OR ({" OR ".join(f"{word}*" for word in words)})'

    def _process_fulltext_results(self, results: List[Dict], query: str) -> List[Dict]:
        """
        Process full-text search results with enhanced metadata
        """
        processed = []
        
        for result in results:
            entity = result.get('entity', {})
            entity_types = result.get('entity_types', [])
            relevance_score = result.get('relevance_score', 0.0)
            related_entities = result.get('related_entities', [])
            relationship_types = result.get('relationship_types', [])
            
            # Determine primary entity type
            primary_type = 'unknown'
            for label in entity_types:
                if label.startswith('GitHub'):
                    primary_type = label.replace('GitHub', '').lower()
                    break
            
            processed_result = {
                'id': entity.get('id') or entity.get('github_id'),
                'title': entity.get('name') or entity.get('title') or entity.get('login'),
                'description': self._get_entity_description(entity, primary_type),
                'type': primary_type,
                'url': entity.get('html_url') or entity.get('url'),
                'score': float(relevance_score),
                'metadata': {
                    'entity_types': entity_types,
                    'updated_at': entity.get('updated_at'),
                    'created_at': entity.get('created_at'),
                    'related_count': len(related_entities),
                    'relationship_types': relationship_types,
                    'search_method': 'fulltext'
                }
            }
            processed.append(processed_result)
        
        return processed

    def _get_entity_description(self, entity: Dict, entity_type: str) -> str:
        """
        Get appropriate description based on entity type - supports all GitHub entity types
        """
        description_map = {
            'repository': lambda e: e.get('description', '')[:200],
            'user': lambda e: f"{e.get('bio', '')} {e.get('company', '')}".strip()[:200],
            'organization': lambda e: e.get('description', '')[:200],
            'issue': lambda e: e.get('body', '')[:200],
            'pullrequest': lambda e: e.get('body', '')[:200],
            'pull_request': lambda e: e.get('body', '')[:200],
            'commit': lambda e: e.get('message', '')[:200],
            'branch': lambda e: f"Branch: {e.get('name', '')}",
            'tag': lambda e: f"Tag: {e.get('name', '')} - {e.get('message', '')}".strip(' -')[:200],
            'release': lambda e: e.get('body', '')[:200] or f"Release: {e.get('name', '')}",
            'code_file': lambda e: f"File: {e.get('path', '')} ({e.get('language', 'unknown')})",
            'codefile': lambda e: f"File: {e.get('path', '')} ({e.get('language', 'unknown')})",
            'file': lambda e: f"File: {e.get('path', '')} ({e.get('language', 'unknown')})",
            'directory': lambda e: f"Directory: {e.get('path', '')}",
            'comment': lambda e: e.get('body', '')[:200],
            'review': lambda e: f"{e.get('state', '').title()} Review: {e.get('body', '')}".strip(': ')[:200],
            'review_comment': lambda e: f"Review comment on {e.get('path', '')}: {e.get('body', '')}".strip(': ')[:200],
            'team': lambda e: f"Team: {e.get('name', '')} - {e.get('description', '')}".strip(' -')[:200]
        }

        description_func = description_map.get(entity_type.lower())
        if description_func:
            return description_func(entity)

        # Fallback to common properties
        return (entity.get('description', '') or
                entity.get('body', '') or
                entity.get('name', '') or
                entity.get('title', '') or '')[:200]

    def _build_graph_query(self, query: str, organisation_id: str,
                          search_type: GitHubSearchType, filters: Dict[str, Any],
                          limit: int) -> Tuple[str, Dict]:
        """Build optimized Cypher query based on search type using schema-driven approach"""

        base_conditions = "n.organisation_id = $organisation_id"
        params = {'organisation_id': organisation_id, 'limit': limit}

        # Handle ALL search type
        if search_type == GitHubSearchType.ALL:
            return self._build_generic_search_query(query, organisation_id, filters, limit)

        # Get the Neo4j label for the search type
        neo4j_label = self.entity_types.get(search_type.value)
        if not neo4j_label:
            logger.warning(f"Unknown search type: {search_type.value}, falling back to generic search")
            return self._build_generic_search_query(query, organisation_id, filters, limit)

        # Build entity-specific query
        return self._build_entity_specific_query(
            query, organisation_id, search_type, neo4j_label, filters, limit
        )

    def _build_entity_specific_query(self, query: str, organisation_id: str,
                                   search_type: GitHubSearchType, neo4j_label: str,
                                   filters: Dict[str, Any], limit: int) -> Tuple[str, Dict]:
        """Build query for specific entity type with comprehensive property search"""

        params = {'organisation_id': organisation_id, 'limit': limit}

        # Get searchable properties for this entity type
        searchable_props = self._get_searchable_properties(neo4j_label)

        # Build WHERE conditions
        where_conditions = ["n.organisation_id = $organisation_id"]

        # Add query text conditions if query is provided
        if query.strip():
            query_conditions = []
            for prop in searchable_props:
                query_conditions.append(f"toLower(toString(n.{prop})) CONTAINS $query")

            if query_conditions:
                where_conditions.append(f"({' OR '.join(query_conditions)})")

            params['query'] = query.lower()

        # Add filter conditions
        filter_conditions = self._build_filter_conditions(filters, search_type)
        where_conditions.extend(filter_conditions['conditions'])
        params.update(filter_conditions['params'])

        # Build relationship joins for context
        relationship_joins = self._build_relationship_joins(neo4j_label)

        # Build ORDER BY clause
        order_by = self._build_order_by_clause(neo4j_label)

        # Construct the full query
        cypher_query = f"""
        MATCH (n:{neo4j_label})
        {relationship_joins['optional_matches']}
        WHERE {' AND '.join(where_conditions)}
        RETURN n as entity,
               {relationship_joins['return_fields']}
               '{search_type.value}' as type
        {order_by}
        LIMIT $limit
        """

        return cypher_query, params

    def _build_generic_search_query(self, query: str, organisation_id: str,
                                  filters: Dict[str, Any], limit: int) -> Tuple[str, Dict]:
        """Build generic search query across all GitHub entities"""

        params = {'organisation_id': organisation_id, 'limit': limit}

        where_conditions = ["n.organisation_id = $organisation_id"]
        where_conditions.append("any(label in labels(n) WHERE label STARTS WITH 'GitHub')")

        if query.strip():
            # Search across common properties
            common_props = ['name', 'title', 'description', 'body', 'message', 'login']
            query_conditions = []
            for prop in common_props:
                query_conditions.append(f"(n.{prop} IS NOT NULL AND toLower(toString(n.{prop})) CONTAINS $query)")

            where_conditions.append(f"({' OR '.join(query_conditions)})")
            params['query'] = query.lower()

        cypher_query = f"""
        MATCH (n)
        WHERE {' AND '.join(where_conditions)}
        WITH n, labels(n) as entity_types,
             CASE
               WHEN n.name IS NOT NULL AND toLower(toString(n.name)) CONTAINS $query THEN 1.0
               WHEN n.title IS NOT NULL AND toLower(toString(n.title)) CONTAINS $query THEN 0.9
               WHEN n.description IS NOT NULL AND toLower(toString(n.description)) CONTAINS $query THEN 0.8
               WHEN n.body IS NOT NULL AND toLower(toString(n.body)) CONTAINS $query THEN 0.7
               ELSE 0.5
             END as relevance_score
        RETURN n as entity, entity_types, 'generic' as type, relevance_score
        ORDER BY relevance_score DESC, n.updated_at DESC
        LIMIT $limit
        """

        return cypher_query, params

    def _get_searchable_properties(self, neo4j_label: str) -> List[str]:
        """Get searchable text properties for a given entity type"""

        # Default searchable properties for all entities
        default_props = ['name', 'title', 'description', 'body']

        # Entity-specific searchable properties-TODO : take from YML file
        entity_specific_props = {
            'GitHubRepository': ['name', 'full_name', 'description', 'language'],
            'GitHubUser': ['login', 'name', 'email', 'bio'],
            'GitHubOrganization': ['login', 'name', 'description'],
            'GitHubIssue': ['title', 'body'],
            'GitHubPullRequest': ['title', 'body', 'head_branch', 'base_branch'],
            'GitHubCommit': ['message', 'author_name', 'author_email'],
            'GitHubBranch': ['name'],
            'GitHubTag': ['name', 'message'],
            'GitHubRelease': ['name', 'tag_name', 'body'],
            'GitHubCodeFile': ['name', 'path', 'language'],
            'GitHubDirectory': ['name', 'path'],
            'GitHubComment': ['body'],
            'GitHubReview': ['body', 'state'],
            'GitHubReviewComment': ['body', 'path'],
            'GitHubTeam': ['name', 'slug', 'description']
        }

        return entity_specific_props.get(neo4j_label, default_props)

    def _build_filter_conditions(self, filters: Dict[str, Any],
                               search_type: GitHubSearchType) -> Dict[str, Any]:
        """Build WHERE conditions from filters"""

        conditions = []
        params = {}

        if not filters:
            return {'conditions': conditions, 'params': params}

        # Handle common filters
        if 'author' in filters:
            conditions.append("""
                EXISTS {
                    MATCH (n)-[:AUTHORED_BY|CREATES_ISSUE|CREATES_PULL_REQUEST]->(author:GitHubUser)
                    WHERE toLower(author.login) = toLower($author_filter)
                }
            """)
            params['author_filter'] = filters['author']

        if 'repository' in filters:
            conditions.append("""
                EXISTS {
                    MATCH (n)-[:BELONGS_TO]->(repo:GitHubRepository)
                    WHERE toLower(repo.name) = toLower($repo_filter)
                }
            """)
            params['repo_filter'] = filters['repository']

        if 'state' in filters and search_type in [GitHubSearchType.ISSUE, GitHubSearchType.PULL_REQUEST]:
            conditions.append("toLower(n.state) = toLower($state_filter)")
            params['state_filter'] = filters['state']

        if 'language' in filters and search_type in [GitHubSearchType.REPOSITORY, GitHubSearchType.CODE_FILE]:
            conditions.append("toLower(n.language) = toLower($language_filter)")
            params['language_filter'] = filters['language']

        return {'conditions': conditions, 'params': params}

    def _build_relationship_joins(self, neo4j_label: str) -> Dict[str, str]:
        """Build OPTIONAL MATCH clauses for entity relationships"""

        optional_matches = []
        return_fields = []

        # Common relationships for context
        if neo4j_label in ['GitHubIssue', 'GitHubPullRequest', 'GitHubCommit']:
            optional_matches.append("OPTIONAL MATCH (n)-[:BELONGS_TO]->(repo:GitHubRepository)")
            return_fields.append("repo")

        if neo4j_label in ['GitHubIssue', 'GitHubPullRequest']:
            optional_matches.append("OPTIONAL MATCH (n)-[:AUTHORED_BY]->(author:GitHubUser)")
            return_fields.append("author")

            optional_matches.append("OPTIONAL MATCH (n)-[:ASSIGNED_TO]->(assignee:GitHubUser)")
            return_fields.append("assignee")

        if neo4j_label == 'GitHubCommit':
            optional_matches.append("OPTIONAL MATCH (n)-[:AUTHORED_BY]->(author:GitHubUser)")
            return_fields.append("author")

        if neo4j_label == 'GitHubRepository':
            optional_matches.append("OPTIONAL MATCH (n)<-[:OWNS_REPOSITORY]-(owner:GitHubUser)")
            return_fields.append("owner")

        if neo4j_label in ['GitHubCodeFile', 'GitHubDirectory']:
            optional_matches.append("OPTIONAL MATCH (n)-[:BELONGS_TO]->(repo:GitHubRepository)")
            return_fields.append("repo")

        return {
            'optional_matches': '\n        '.join(optional_matches),
            'return_fields': ', '.join(return_fields) + ',' if return_fields else ''
        }

    def _build_order_by_clause(self, neo4j_label: str) -> str:
        """Build ORDER BY clause based on entity type"""

        order_clauses = {
            'GitHubRepository': 'ORDER BY n.stargazers_count DESC, n.updated_at DESC',
            'GitHubIssue': 'ORDER BY n.updated_at DESC',
            'GitHubPullRequest': 'ORDER BY n.updated_at DESC',
            'GitHubCommit': 'ORDER BY n.date DESC',
            'GitHubUser': 'ORDER BY n.followers_count DESC, n.updated_at DESC',
            'GitHubRelease': 'ORDER BY n.published_at DESC',
            'GitHubTag': 'ORDER BY n.created_at DESC'
        }

        return order_clauses.get(neo4j_label, 'ORDER BY n.updated_at DESC')

    def _generate_cache_key(self, query: str, organisation_id: str,
                           search_type: GitHubSearchType, strategy: SearchStrategy,
                           filters: Dict[str, Any]) -> str:
        """Generate cache key for search results"""
        key_data = {
            'query': query,
            'organisation_id': organisation_id,
            'search_type': search_type.value if search_type else None,
            'strategy': strategy.value,
            'filters': filters or {}
        }
        return f"github_search:{hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()}"

    def _extract_entities_from_query(self, query: str) -> List[str]:
        """Extract potential entity names from query"""
        # Simple entity extraction - can be enhanced with NLP
        words = query.split()
        entities = []
        
        # Look for capitalized words (potential proper nouns)
        for word in words:
            if word[0].isupper() and len(word) > 2:
                entities.append(word.lower())
        
        return entities

    def _extract_relationship_patterns(self, query: str) -> List[str]:
        """Extract relationship patterns from query"""
        patterns = []
        query_lower = query.lower()
        
        relationship_keywords = {
            'assigned': 'ASSIGNED',
            'authored': 'AUTHORED',
            'reviewed': 'REVIEWED',
            'owns': 'OWNS',
            'member': 'MEMBER',
            'belongs': 'BELONGS'
        }
        
        for keyword, pattern in relationship_keywords.items():
            if keyword in query_lower:
                patterns.append(pattern)
        
        return patterns

    def _process_graph_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process and format graph search results"""
        processed = []
        
        for result in results:
            entity = result.get('entity', {})
            processed_result = {
                'id': entity.get('id') or entity.get('github_id'),
                'title': entity.get('name') or entity.get('title'),
                'description': entity.get('description') or entity.get('body', '')[:200],
                'type': result.get('type', 'unknown'),
                'url': entity.get('html_url'),
                'score': self._calculate_relevance_score(entity, query),
                'metadata': {
                    'entity_type': result.get('entity_types', []),
                    'updated_at': entity.get('updated_at'),
                    'created_at': entity.get('created_at')
                }
            }
            processed.append(processed_result)
        
        return sorted(processed, key=lambda x: x['score'], reverse=True)

    def _calculate_relevance_score(self, entity: Dict, query: str) -> float:
        """Calculate relevance score for search results"""
        score = 0.0
        query_lower = query.lower()
        
        # Title/name match
        if entity.get('name', '').lower() == query_lower:
            score += 1.0
        elif query_lower in entity.get('name', '').lower():
            score += 0.8
        
        # Description match
        if query_lower in entity.get('description', '').lower():
            score += 0.6
        
        # Boost for recent updates
        if entity.get('updated_at'):
            try:
                updated = datetime.fromisoformat(entity['updated_at'].replace('Z', '+00:00'))
                days_old = (datetime.now() - updated.replace(tzinfo=None)).days
                if days_old < 30:
                    score += 0.2
            except:
                pass
        
        return min(score, 1.0)

    # Additional helper methods for processing different result types...
    def _process_entity_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process entity-centric search results"""
        # Implementation similar to _process_graph_results but with entity focus
        return self._process_graph_results(results, query)

    def _process_relationship_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process relationship-centric search results"""
        # Implementation for relationship results
        return self._process_graph_results(results, query)

    def _process_fallback_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Process fallback search results"""
        return self._process_graph_results(results, query)

    def _merge_search_results(self, results: List[Dict], query: str) -> List[Dict]:
        """Merge results from multiple search strategies"""
        merged = []
        seen_ids = set()
        
        for result_set in results:
            if isinstance(result_set, dict) and result_set.get('success'):
                for item in result_set.get('results', []):
                    item_id = item.get('id')
                    if item_id and item_id not in seen_ids:
                        seen_ids.add(item_id)
                        merged.append(item)
        
        return sorted(merged, key=lambda x: x.get('score', 0), reverse=True)

    def _enhance_query_for_github(self, query: str, search_type: GitHubSearchType) -> str:
        """Enhance query with GitHub-specific context for all entity types"""

        enhancement_map = {
            GitHubSearchType.REPOSITORY: f"GitHub repository: {query}",
            GitHubSearchType.USER: f"GitHub user: {query}",
            GitHubSearchType.ORGANIZATION: f"GitHub organization: {query}",
            GitHubSearchType.CODE_FILE: f"GitHub code file: {query}",
            GitHubSearchType.DIRECTORY: f"GitHub directory: {query}",
            GitHubSearchType.ISSUE: f"GitHub issue: {query}",
            GitHubSearchType.PULL_REQUEST: f"GitHub pull request: {query}",
            GitHubSearchType.COMMIT: f"GitHub commit: {query}",
            GitHubSearchType.BRANCH: f"GitHub branch: {query}",
            GitHubSearchType.TAG: f"GitHub tag: {query}",
            GitHubSearchType.RELEASE: f"GitHub release: {query}",
            GitHubSearchType.COMMENT: f"GitHub comment: {query}",
            GitHubSearchType.REVIEW: f"GitHub review: {query}",
            GitHubSearchType.REVIEW_COMMENT: f"GitHub review comment: {query}",
            GitHubSearchType.TEAM: f"GitHub team: {query}",
        }

        return enhancement_map.get(search_type, f"GitHub: {query}")

    def _filter_vector_results(self, results: List[Dict], organisation_id: str, 
                              search_type: GitHubSearchType) -> List[Dict]:
        """Filter vector search results for GitHub entities"""
        filtered = []
        
        for result in results:
            metadata = result.get('metadata', {})
            if (metadata.get('organisation_id') == organisation_id and 
                metadata.get('source_type') == 'github'):
                filtered.append({
                    'id': result.get('id'),
                    'title': metadata.get('title', ''),
                    'description': result.get('content', '')[:200],
                    'type': metadata.get('entity_type', 'unknown'),
                    'url': metadata.get('url'),
                    'score': result.get('score', 0.0),
                    'metadata': metadata
                })
        
        return filtered

    async def _specialized_entity_search(self, query: str, organisation_id: str,
                                       search_type: GitHubSearchType, limit: int) -> Dict[str, Any]:
        """
        Execute specialized search using entity-specific full-text indexes for all supported types
        """
        start_time = datetime.now()

        try:
            # Comprehensive mapping of search types to index names
            index_mapping = {
                GitHubSearchType.REPOSITORY: 'githubRepositoryIndex',
                GitHubSearchType.USER: 'githubUserIndex',
                GitHubSearchType.ORGANIZATION: 'githubOrganizationIndex',
                GitHubSearchType.ISSUE: 'githubIssueIndex',
                GitHubSearchType.PULL_REQUEST: 'githubPullRequestIndex',
                GitHubSearchType.COMMIT: 'githubCommitIndex',
                GitHubSearchType.CODE_FILE: 'githubCodeFileIndex',
                GitHubSearchType.DIRECTORY: 'githubDirectoryIndex',
                GitHubSearchType.BRANCH: 'githubBranchIndex',
                GitHubSearchType.TAG: 'githubTagIndex',
                GitHubSearchType.RELEASE: 'githubReleaseIndex',
                GitHubSearchType.COMMENT: 'githubCommentIndex',
                GitHubSearchType.REVIEW: 'githubReviewIndex',
                GitHubSearchType.REVIEW_COMMENT: 'githubReviewCommentIndex',
                GitHubSearchType.TEAM: 'githubTeamIndex'
            }

            index_name = index_mapping.get(search_type, 'githubUnifiedIndex')
            search_query = self._prepare_fulltext_query(query)

            # Build entity-specific context queries
            context_query = self._build_context_query_for_entity(search_type)

            cypher_query = f"""
            CALL db.index.fulltext.queryNodes('{index_name}', $search_query)
            YIELD node, score
            WHERE node.organisation_id = $organisation_id

            {context_query}

            RETURN node as entity,
                   labels(node) as entity_types,
                   score as relevance_score,
                   collect(DISTINCT related)[0..5] as related_entities,
                   collect(DISTINCT type(r))[0..5] as relationship_types
            ORDER BY score DESC
            LIMIT $limit
            """

            params = {
                'search_query': search_query,
                'organisation_id': organisation_id,
                'limit': limit
            }

            results = execute_read_query(cypher_query, params)
            processed_results = self._process_fulltext_results(results, query)

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            return {
                'success': True,
                'strategy': f'specialized_{search_type.value}',
                'results': processed_results,
                'total_count': len(processed_results),
                'execution_time_ms': execution_time,
                'metadata': {
                    'index_used': index_name,
                    'search_query': search_query
                }
            }

        except Exception as e:
            logger.error(f"Specialized entity search failed: {str(e)}")
            return await self._fallback_search(query, organisation_id, limit)

    def _build_context_query_for_entity(self, search_type: GitHubSearchType) -> str:
        """Build context query to get related entities for specific search types"""

        context_queries = {
            GitHubSearchType.REPOSITORY: """
                OPTIONAL MATCH (node)<-[:OWNS_REPOSITORY]-(owner:GitHubUser)
                OPTIONAL MATCH (node)<-[:BELONGS_TO]-(issues:GitHubIssue)
                OPTIONAL MATCH (node)<-[:BELONGS_TO]-(prs:GitHubPullRequest)
                WITH node, score, owner, count(issues) as issue_count, count(prs) as pr_count
                OPTIONAL MATCH (node)-[r]-(related)
                WHERE related.organisation_id = $organisation_id
            """,
            GitHubSearchType.ISSUE: """
                OPTIONAL MATCH (node)-[:BELONGS_TO]->(repo:GitHubRepository)
                OPTIONAL MATCH (node)-[:AUTHORED_BY]->(author:GitHubUser)
                OPTIONAL MATCH (node)-[:ASSIGNED_TO]->(assignee:GitHubUser)
                WITH node, score, repo, author, assignee
                OPTIONAL MATCH (node)-[r]-(related)
                WHERE related.organisation_id = $organisation_id
            """,
            GitHubSearchType.PULL_REQUEST: """
                OPTIONAL MATCH (node)-[:BELONGS_TO]->(repo:GitHubRepository)
                OPTIONAL MATCH (node)-[:AUTHORED_BY]->(author:GitHubUser)
                OPTIONAL MATCH (node)<-[:REVIEWS_PULL_REQUEST]-(reviewer:GitHubUser)
                WITH node, score, repo, author, collect(reviewer)[0..3] as reviewers
                OPTIONAL MATCH (node)-[r]-(related)
                WHERE related.organisation_id = $organisation_id
            """,
            GitHubSearchType.COMMIT: """
                OPTIONAL MATCH (node)-[:BELONGS_TO]->(repo:GitHubRepository)
                OPTIONAL MATCH (node)-[:AUTHORED_BY]->(author:GitHubUser)
                OPTIONAL MATCH (node)-[:COMMITS_TO_BRANCH]->(branch:GitHubBranch)
                WITH node, score, repo, author, branch
                OPTIONAL MATCH (node)-[r]-(related)
                WHERE related.organisation_id = $organisation_id
            """
        }

        return context_queries.get(search_type, """
            OPTIONAL MATCH (node)-[r]-(related)
            WHERE related.organisation_id = $organisation_id
        """)
