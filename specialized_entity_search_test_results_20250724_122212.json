{"total_tests": 4, "passed_tests": 3, "failed_tests": 1, "test_details": [{"name": "Repository Specialized Search", "passed": true, "execution_time": 0.23757696151733398, "results_count": 15, "expected_min_results": 1, "strategy_used": "graph_traversal", "expected_strategies": ["graph_traversal", "simple_property_fallback", "specialized_repository"], "strategy_matches": true, "success": true, "service_execution_time_ms": 237.44799999999998, "sample_results": [{"id": "github_repo_test_org_8d94b63f_1", "title": "specialized-search-test", "description": "Repository for testing specialized entity search functionality", "type": "repository", "url": "https://github.com/test-org/specialized-search-test", "score": 1.0, "metadata": {"entity_type": [], "updated_at": "2025-07-24T06:52:10.605000000+00:00", "created_at": "2025-07-24T06:52:10.605000000+00:00"}}], "error": null}, {"name": "User Specialized Search", "passed": true, "execution_time": 0.20430517196655273, "results_count": 1, "expected_min_results": 1, "strategy_used": "graph_traversal", "expected_strategies": ["graph_traversal", "simple_property_fallback", "specialized_user"], "strategy_matches": true, "success": true, "service_execution_time_ms": 204.186, "sample_results": [{"id": "github_user_test_user_ed07a388", "title": "Specialized Search Tester", "description": "", "type": "user", "url": "https://github.com/specialized-tester", "score": 0.0, "metadata": {"entity_type": [], "updated_at": "2025-07-24T06:52:10.740000000+00:00", "created_at": "2025-07-24T06:52:10.740000000+00:00"}}], "error": null}, {"name": "Issue Specialized Search", "passed": true, "execution_time": 0.2259082794189453, "results_count": 3, "expected_min_results": 1, "strategy_used": "graph_traversal", "expected_strategies": ["graph_traversal", "simple_property_fallback", "specialized_issue"], "strategy_matches": true, "success": true, "service_execution_time_ms": 225.78599999999997, "sample_results": [{"id": 1186643563, "title": "Website not work in MetaMask Android APP", "description": "**Bug Description**\nThe Relaychain website cannot be used in MetaMask Android APP, because I cannot connect to the wallet.\n\n**Steps to Reproduce**\n1. Open MetaMask Android Wallet\n2. Click on the web3 ", "type": "issue", "url": "https://github.com/RelayChain/relay-app/issues/112", "score": 0.0, "metadata": {"entity_type": [], "updated_at": "2022-03-30T15:37:34Z", "created_at": "2022-03-30T15:37:07Z"}}], "error": null}, {"name": "Generic Search (No Specialization)", "passed": false, "execution_time": 0.7790920734405518, "results_count": 3, "expected_min_results": 1, "strategy_used": "hybrid", "expected_strategies": ["graph_traversal", "simple_property_fallback"], "strategy_matches": false, "success": true, "service_execution_time_ms": 779.004, "sample_results": [{"id": "github_repo_test_org_8d94b63f_1", "title": "specialized-search-test", "description": "Repository for testing specialized entity search functionality", "type": "generic", "url": "https://github.com/test-org/specialized-search-test", "score": 1.0, "metadata": {"entity_type": ["GitHubRepository"], "updated_at": "2025-07-24T06:52:10.605000000+00:00", "created_at": "2025-07-24T06:52:10.605000000+00:00"}}], "error": null}]}